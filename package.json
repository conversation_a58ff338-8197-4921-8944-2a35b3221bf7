{"name": "tango-community", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"start": "yarn workspace playground dev", "start:docs": "yarn workspace storybook storybook", "build": "lerna run build", "typedoc": "typedoc", "docs": "yarn typedoc && open docs/index.html", "test": "jest", "test:watch": "jest --watch", "eslint": "eslint packages/**/src/*.{ts,tsx} --cache", "publish": "lerna <PERSON>", "ver": "lerna version --no-private --conventional-commits", "release": "yarn eslint && yarn build && yarn ver && lerna publish from-git", "release:npm": "yarn eslint && yarn build && yarn ver && lerna publish from-package", "release:beta": "yarn eslint && yarn build && yarn ver && lerna publish from-package --dist-tag beta", "up": "yarn upgrade-interactive --latest", "prepare": "husky install"}, "devDependencies": {"@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/jest": "^29.5.7", "@types/lodash-es": "^4.17.10", "@types/lodash.get": "^4.4.8", "@types/lodash.isequal": "^4.5.7", "@types/lodash.set": "^4.3.8", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@types/styled-components": "^5.1.29", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "conventional-changelog-cli": "^4.1.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-ali": "^14.0.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "http-server": "^14.1.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lerna": "^8.1.2", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "react": "^17.0.0", "react-dom": "^17.0.0", "styled-components": "^5.3.6", "typedoc": "^0.26.7", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}