import { action, computed, makeObservable, observable, toJS } from 'mobx';
import { isNil, Dict, SLOT } from '@music163/tango-helpers';
import * as nodeHtmlParser from 'node-html-parser';
import type {
  IFileConfig,
  IImportSpecifierSourceData,
  IImportSpecifierData,
  InsertChildPositionType,
  IViewNodeData
} from '../types';
import { AbstractWorkspace } from './abstract-workspace';
import { AbstractFile } from './abstract-file';
import { VueViewNode } from './view-node';
import { IViewFile } from './interfaces';
import {
  vue2ast,
  traverseVueTemplate,
  ast2vue,
  IdGenerator,
  updateVueTemplateNodeAttributes,
  insertSiblingBeforeTargetNode,
  insertSiblingAfterTargetNode,
  appendChildToTargetNode,
  removeTargetNode
} from '../helpers';
import {
  parseVueScript,
  addVariableToVueScript,
  updateVariableInVueScript,
  type VueScriptAnalysis,
  type VueScriptVariable,
  type VueScriptMethod,
  type VueScriptImport
} from '../helpers/ast/vue-script-traverse';

/**
 * 将节点列表转换为 tree data 嵌套数组
 * @param list
 */
function nodeListToTreeData(list: IViewNodeData[]) {
  const map: Record<string, IViewNodeData> = {};

  list.forEach((item) => {
    // 如果不存在，则初始化
    if (!map[item.id]) {
      map[item.id] = {
        ...item,
        children: [],
      };
    }

    // 是否找到父节点，找到则塞进去
    if (item.parentId && map[item.parentId]) {
      map[item.parentId].children.push(map[item.id]);
    }
  });

  // 保留根节点
  const ret = Object.values(map).filter((item) => !item.parentId);
  return ret;
}

export class VueViewFile extends AbstractFile implements IViewFile {
  /**
   * 描述整个Vue单文件组件的AST，通过 @vue/compiler-sfc 解析获得
   */
  vueSfcAst: any;

  /**
   * Vue SFC中的<template>部分的AST
   */
  vueTemplateAst: any;

  /**
   * Vue SFC中的<script>部分的分析结果
   */
  vueScriptAnalysis: VueScriptAnalysis;

  /**
   * ID 生成器
   */
  idGenerator: IdGenerator;

  /**
   * 通过导入组件名查找组件来自的包
   */
  importMap?: Dict<IImportSpecifierSourceData>;

  /**
   * 解析为树结构的节点数组
   */
  private _nodesTree: IViewNodeData[] = [];

  /**
   * 节点列表 <id, Node>
   */
  private _nodes: Map<string, VueViewNode>;

  get nodes() {
    return this._nodes;
  }

  get nodesTree() {
    return toJS(this._nodesTree);
  }

  get tree() {
    return this.vueSfcAst;
  }

  constructor(workspace: AbstractWorkspace, props: IFileConfig) {
    super(workspace, props, false);
    this.idGenerator = new IdGenerator({ prefix: props.filename });
    this._nodes = new Map();
    this._nodesTree = [];
    this.importMap = {};
    this.vueScriptAnalysis = {
      variables: [],
      methods: [],
      imports: [],
      isSetupScript: false,
      ast: null,
    };
    this.update(props.code);
    makeObservable(this, {
      _code: observable,
      _cleanCode: observable,
      code: computed,
      cleanCode: computed,
      nodesTree: computed,
      tree: computed,
      update: action,
      vueScriptAnalysis: observable,
    });
  }

  update(code?: string, isFormatCode?: boolean, refreshWorkspace?: boolean) {
    this.lastModified = Date.now();

    if (!isNil(code)) {
      this._syncByCode(code);
    } else {
      this._syncByAst();
    }

    if (refreshWorkspace !== false) {
      this.workspace.onFilesChange([this.filename]);
    }
  }

  /**
   * 基于最新的 ast 进行源码同步
   */
  _syncByAst() {
    const code = ast2vue(this.vueSfcAst);
    this._syncByCode(code);
  }

  /**
   * 基于输入的源码进行同步
   * @param code 源码
   * @returns
   */
  _syncByCode(code: string) {
    if (code === this._code) {
      return;
    }

    this.vueSfcAst = vue2ast(code);
    this.vueTemplateAst = this.vueSfcAst.template;

    // 解析script部分
    const scriptContent = this.vueSfcAst.scriptSetup?.content || this.vueSfcAst.script?.content || '';
    const isSetupScript = !!this.vueSfcAst.scriptSetup;
    this.vueScriptAnalysis = parseVueScript(scriptContent, isSetupScript);

    const {
      designerAst,
      cleanAst,
      nodes
    } = traverseVueTemplate(this.vueSfcAst, this.idGenerator);

    this._code = ast2vue(designerAst);
    this._cleanCode = ast2vue(cleanAst);

    this._nodes.clear();
    nodes.forEach((cur) => {
      const node = new VueViewNode({
        id: cur.id,
        component: cur.component,
        rawNode: cur.rawNode,
        file: this,
      });
      this._nodes.set(cur.id, node);
    });

    // 构建节点树
    this._nodesTree = nodeListToTreeData(nodes);
  }

  getNode(nodeId: string) {
    return this._nodes.get(nodeId);
  }

  /**
   * 更新节点的单个属性
   * @param nodeId 节点ID
   * @param attrName 属性名
   * @param attrValue 属性值
   * @param relatedImports 相关导入
   */
  updateNodeAttribute(
    nodeId: string,
    attrName: string,
    attrValue?: any,
    relatedImports?: string[],
  ) {
    return this.updateNodeAttributes(nodeId, { [attrName]: attrValue }, relatedImports);
  }

  updateNodeAttributes(nodeId: string, config: Record<string, any>, relatedImports?: string[]) {
    // TODO: 处理relatedImports，添加相关的导入语句
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _ = relatedImports; // 暂时忽略未使用的参数
    this.vueSfcAst = updateVueTemplateNodeAttributes(this.vueSfcAst, nodeId, config);

    return this;
  }

  insertBefore(targetNodeId: string, newNode: any) {
    this.vueSfcAst = insertSiblingBeforeTargetNode(this.vueSfcAst, targetNodeId, newNode);

    return this;
  }

  insertAfter(targetNodeId: string, newNode: any) {
    this.vueSfcAst = insertSiblingAfterTargetNode(this.vueSfcAst, targetNodeId, newNode);

    return this;
  }

  insertChild(
    targetNodeId: string,
    newNode: any,
    position?: InsertChildPositionType,
  ) {
    // TODO: 支持position参数，目前appendChildToTargetNode不支持position
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _ = position; // 暂时忽略未使用的参数
    this.vueSfcAst = appendChildToTargetNode(this.vueSfcAst, targetNodeId, newNode);
    return this;
  }

  /**
   * 删除节点
   * @param nodeId
   */
  removeNode(nodeId: string) {
    this.vueSfcAst = removeTargetNode(this.vueSfcAst, nodeId);

    return this;
  }

  /**
   * 替换目标节点为新节点
   * @param targetNodeId 目标节点ID
   * @param newNode 新节点
   */
  replaceNode(targetNodeId: string, newNode: any) {
    // TODO: 实现Vue节点替换逻辑
    // 目前先简单地删除旧节点，然后在同一位置插入新节点
    const templateRootNode = nodeHtmlParser.parse(this.vueSfcAst?.template?.content);
    const targetNode = templateRootNode.querySelector(`[${SLOT.dnd}="${targetNodeId}"]`);

    if (targetNode) {
      targetNode.insertAdjacentHTML('beforebegin', newNode);
      targetNode.remove();
      this.vueSfcAst.template.content = templateRootNode.toString();
    }

    return this;
  }

  /**
   * 替换视图的子元素
   * @param rawNodes 新的子节点数组
   */
  replaceViewChildren(rawNodes: any[]) {
    // TODO: 实现Vue视图子元素替换逻辑
    // 这个方法在Vue中可能需要特殊处理，因为Vue的模板结构与JSX不同
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _ = rawNodes; // 暂时忽略未使用的参数
    // TODO: 移除console.warn，这里只是为了提醒开发者该方法尚未完全实现
    return this;
  }

  /**
   * 添加导入符号
   * @param source
   * @param newSpecifiers
   * @returns
   */
  addImportSpecifiers(source: string, newSpecifiers: IImportSpecifierData[]) {
    // TODO: 实现Vue文件的导入处理逻辑
    // Vue文件的导入通常在<script>部分，需要解析和修改<script>标签内容
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _source = source; // 暂时忽略未使用的参数
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _newSpecifiers = newSpecifiers; // 暂时忽略未使用的参数
    return this;
  }

  /**
   * 判断节点是否存在
   * @param codeId 节点的代码ID
   */
  hasNodeByCodeId?(codeId: string): boolean {
    // TODO: 实现基于codeId的节点查找逻辑
    for (const [, node] of this._nodes) {
      if (node.rawNode?.getAttribute?.('tid') === codeId) {
        return true;
      }
    }
    return false;
  }

  /**
   * 列出导入源
   */
  listImportSources?(): string[] {
    // TODO: 实现Vue文件导入源列表逻辑
    return Object.keys(this.importMap || {});
  }

  /**
   * 列出模态框
   */
  listModals?(): Array<{ label: string; value: string }> {
    const modals: Array<{ label: string; value: string }> = [];
    const activeViewNodes = this.nodes || new Map();

    Array.from(activeViewNodes.values()).forEach((node) => {
      if (['nb-dialog', 'nb-drawer', 'nb-modal'].includes(node.component) && node.props.id) {
        modals.push({
          label: `${node.component}(${node.props.id})`,
          value: node.props.id,
        });
      }
    });

    return modals;
  }

  /**
   * 列出表单
   */
  listForms?(): Record<string, string[]> {
    // TODO: 实现Vue文件表单列表逻辑
    return {};
  }

  /**
   * 获取script中的所有变量
   */
  getScriptVariables(): VueScriptVariable[] {
    return this.vueScriptAnalysis.variables || [];
  }

  /**
   * 获取script中的所有方法
   */
  getScriptMethods(): VueScriptMethod[] {
    return this.vueScriptAnalysis.methods || [];
  }

  /**
   * 获取script中的所有导入
   */
  getScriptImports(): VueScriptImport[] {
    return this.vueScriptAnalysis.imports || [];
  }

  /**
   * 获取可用于数据绑定的变量列表
   */
  getDataBindingVariables(): Array<{ label: string; value: string; type: string }> {
    const variables = this.getScriptVariables();
    return variables
      .filter(variable =>
        variable.type !== 'function' &&
        variable.type !== 'import' &&
        !variable.name.startsWith('_') // 过滤私有变量
      )
      .map(variable => ({
        label: `${variable.name} (${variable.type})`,
        value: variable.name,
        type: variable.type,
      }));
  }

  /**
   * 获取可用于事件绑定的方法列表
   */
  getEventBindingMethods(): Array<{ label: string; value: string; params: string[] }> {
    const methods = this.getScriptMethods();
    return methods.map(method => ({
      label: `${method.name}(${method.params.join(', ')})`,
      value: method.name,
      params: method.params,
    }));
  }

  /**
   * 添加变量到script中
   */
  addScriptVariable(
    variableName: string,
    variableType: 'ref' | 'reactive' | 'const' | 'let',
    initialValue?: any
  ): this {
    const scriptContent = this.vueSfcAst.scriptSetup?.content || this.vueSfcAst.script?.content || '';
    const isSetupScript = !!this.vueSfcAst.scriptSetup;

    const newScriptContent = addVariableToVueScript(
      scriptContent,
      variableName,
      variableType,
      initialValue,
      isSetupScript
    );

    if (isSetupScript && this.vueSfcAst.scriptSetup) {
      this.vueSfcAst.scriptSetup.content = newScriptContent;
    } else if (this.vueSfcAst.script) {
      this.vueSfcAst.script.content = newScriptContent;
    }

    // 重新解析script
    this.vueScriptAnalysis = parseVueScript(newScriptContent, isSetupScript);

    return this;
  }

  /**
   * 更新script中的变量值
   */
  updateScriptVariable(variableName: string, newValue: any): this {
    const scriptContent = this.vueSfcAst.scriptSetup?.content || this.vueSfcAst.script?.content || '';
    const isSetupScript = !!this.vueSfcAst.scriptSetup;

    const newScriptContent = updateVariableInVueScript(
      scriptContent,
      variableName,
      newValue
    );

    if (isSetupScript && this.vueSfcAst.scriptSetup) {
      this.vueSfcAst.scriptSetup.content = newScriptContent;
    } else if (this.vueSfcAst.script) {
      this.vueSfcAst.script.content = newScriptContent;
    }

    // 重新解析script
    this.vueScriptAnalysis = parseVueScript(newScriptContent, isSetupScript);

    return this;
  }
}
