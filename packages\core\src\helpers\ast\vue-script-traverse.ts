/**
 * 访问，遍历，修改 Vue SFC 的 <script> 部分
 */
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import generate from '@babel/generator';
import * as t from '@babel/types';

export interface VueScriptVariable {
  name: string;
  type: 'ref' | 'reactive' | 'computed' | 'const' | 'let' | 'var' | 'function' | 'import';
  value?: any;
  isReactive: boolean;
  line?: number;
  column?: number;
}

export interface VueScriptMethod {
  name: string;
  params: string[];
  isAsync: boolean;
  line?: number;
  column?: number;
}

export interface VueScriptImport {
  source: string;
  specifiers: Array<{
    imported: string;
    local: string;
    type: 'default' | 'named' | 'namespace';
  }>;
}

export interface VueScriptAnalysis {
  variables: VueScriptVariable[];
  methods: VueScriptMethod[];
  imports: VueScriptImport[];
  isSetupScript: boolean;
  ast: any;
}

/**
 * 解析Vue SFC的<script>部分
 * @param scriptContent script标签的内容
 * @param isSetupScript 是否是<script setup>
 */
export function parseVueScript(scriptContent: string, isSetupScript = false): VueScriptAnalysis {
  if (!scriptContent || scriptContent.trim() === '') {
    return {
      variables: [],
      methods: [],
      imports: [],
      isSetupScript,
      ast: null,
    };
  }

  try {
    const ast = parse(scriptContent, {
      sourceType: 'module',
      plugins: ['typescript', 'jsx'],
    });

    const variables: VueScriptVariable[] = [];
    const methods: VueScriptMethod[] = [];
    const imports: VueScriptImport[] = [];

    traverse(ast, {
      // 处理导入语句
      ImportDeclaration(path) {
        const source = path.node.source.value;
        const specifiers = path.node.specifiers.map((spec) => {
          if (t.isImportDefaultSpecifier(spec)) {
            return {
              imported: 'default',
              local: spec.local.name,
              type: 'default' as const,
            };
          } else if (t.isImportNamespaceSpecifier(spec)) {
            return {
              imported: '*',
              local: spec.local.name,
              type: 'namespace' as const,
            };
          } else if (t.isImportSpecifier(spec)) {
            return {
              imported: t.isIdentifier(spec.imported) ? spec.imported.name : spec.imported.value,
              local: spec.local.name,
              type: 'named' as const,
            };
          }
          return null;
        }).filter(Boolean);

        imports.push({
          source,
          specifiers: specifiers as any[],
        });
      },

      // 处理变量声明
      VariableDeclaration(path) {
        path.node.declarations.forEach((declaration) => {
          if (t.isIdentifier(declaration.id)) {
            const name = declaration.id.name;
            let type: VueScriptVariable['type'] = path.node.kind as any;
            let isReactive = false;
            let value: any;

            // 检查是否是Vue响应式变量
            if (t.isCallExpression(declaration.init)) {
              const callee = declaration.init.callee;
              if (t.isIdentifier(callee)) {
                if (callee.name === 'ref') {
                  type = 'ref';
                  isReactive = true;
                  // 获取ref的初始值
                  if (declaration.init.arguments.length > 0) {
                    const arg = declaration.init.arguments[0];
                    if (t.isStringLiteral(arg) || t.isNumericLiteral(arg) || t.isBooleanLiteral(arg)) {
                      value = arg.value;
                    } else if (t.isArrayExpression(arg)) {
                      value = '[]';
                    } else if (t.isObjectExpression(arg)) {
                      value = '{}';
                    }
                  }
                } else if (callee.name === 'reactive') {
                  type = 'reactive';
                  isReactive = true;
                  value = '{}';
                } else if (callee.name === 'computed') {
                  type = 'computed';
                  isReactive = true;
                }
              }
            } else if (t.isArrayExpression(declaration.init)) {
              value = '[]';
            } else if (t.isObjectExpression(declaration.init)) {
              value = '{}';
            } else if (t.isStringLiteral(declaration.init)) {
              value = declaration.init.value;
            } else if (t.isNumericLiteral(declaration.init)) {
              value = declaration.init.value;
            } else if (t.isBooleanLiteral(declaration.init)) {
              value = declaration.init.value;
            }

            variables.push({
              name,
              type,
              value,
              isReactive,
              line: path.node.loc?.start.line,
              column: path.node.loc?.start.column,
            });
          }
        });
      },

      // 处理函数声明
      FunctionDeclaration(path) {
        if (t.isIdentifier(path.node.id)) {
          const name = path.node.id.name;
          const params = path.node.params.map((param) => {
            if (t.isIdentifier(param)) {
              return param.name;
            }
            return 'unknown';
          });

          methods.push({
            name,
            params,
            isAsync: path.node.async,
            line: path.node.loc?.start.line,
            column: path.node.loc?.start.column,
          });
        }
      },

      // 处理箭头函数和函数表达式（作为变量）
      VariableDeclarator(path) {
        if (t.isIdentifier(path.node.id) && 
            (t.isArrowFunctionExpression(path.node.init) || t.isFunctionExpression(path.node.init))) {
          const name = path.node.id.name;
          const func = path.node.init;
          const params = func.params.map((param) => {
            if (t.isIdentifier(param)) {
              return param.name;
            }
            return 'unknown';
          });

          methods.push({
            name,
            params,
            isAsync: func.async,
            line: path.node.loc?.start.line,
            column: path.node.loc?.start.column,
          });

          // 同时也作为函数类型的变量
          variables.push({
            name,
            type: 'function',
            isReactive: false,
            line: path.node.loc?.start.line,
            column: path.node.loc?.start.column,
          });
        }
      },
    });

    return {
      variables,
      methods,
      imports,
      isSetupScript,
      ast,
    };
  } catch (error) {
    console.error('解析Vue script失败:', error);
    return {
      variables: [],
      methods: [],
      imports: [],
      isSetupScript,
      ast: null,
    };
  }
}

/**
 * 向Vue script中添加变量
 * @param scriptContent 原始script内容
 * @param variableName 变量名
 * @param variableType 变量类型
 * @param initialValue 初始值
 * @param isSetupScript 是否是setup script
 */
export function addVariableToVueScript(
  scriptContent: string,
  variableName: string,
  variableType: 'ref' | 'reactive' | 'const' | 'let',
  initialValue?: any,
  isSetupScript = false
): string {
  try {
    const ast = parse(scriptContent, {
      sourceType: 'module',
      plugins: ['typescript', 'jsx'],
    });

    // 构建新的变量声明
    let declaration: t.VariableDeclarator;
    
    if (variableType === 'ref') {
      const refCall = t.callExpression(
        t.identifier('ref'),
        initialValue !== undefined ? [t.valueToNode(initialValue)] : []
      );
      declaration = t.variableDeclarator(t.identifier(variableName), refCall);
    } else if (variableType === 'reactive') {
      const reactiveCall = t.callExpression(
        t.identifier('reactive'),
        [t.objectExpression([])]
      );
      declaration = t.variableDeclarator(t.identifier(variableName), reactiveCall);
    } else {
      declaration = t.variableDeclarator(
        t.identifier(variableName),
        initialValue !== undefined ? t.valueToNode(initialValue) : t.nullLiteral()
      );
    }

    const variableDeclaration = t.variableDeclaration(variableType, [declaration]);

    // 将新变量添加到AST中
    ast.body.push(variableDeclaration);

    return generate(ast).code;
  } catch (error) {
    console.error('添加变量到Vue script失败:', error);
    return scriptContent;
  }
}

/**
 * 更新Vue script中的变量值
 * @param scriptContent 原始script内容
 * @param variableName 变量名
 * @param newValue 新值
 */
export function updateVariableInVueScript(
  scriptContent: string,
  variableName: string,
  newValue: any
): string {
  try {
    const ast = parse(scriptContent, {
      sourceType: 'module',
      plugins: ['typescript', 'jsx'],
    });

    traverse(ast, {
      VariableDeclarator(path) {
        if (t.isIdentifier(path.node.id) && path.node.id.name === variableName) {
          // 更新变量的初始值
          if (t.isCallExpression(path.node.init)) {
            // 如果是ref()或reactive()调用，更新参数
            const callee = path.node.init.callee;
            if (t.isIdentifier(callee) && (callee.name === 'ref' || callee.name === 'reactive')) {
              path.node.init.arguments = [t.valueToNode(newValue)];
            }
          } else {
            // 直接更新值
            path.node.init = t.valueToNode(newValue);
          }
        }
      },
    });

    return generate(ast).code;
  } catch (error) {
    console.error('更新Vue script变量失败:', error);
    return scriptContent;
  }
}
