import React, { useCallback, useMemo, useState } from 'react';
import { css, Box, Text } from 'coral-system';
import { Input, Select, Button, Space, Modal, Form } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { FormItemComponentProps } from '@music163/tango-setting-form';
import { useWorkspace } from '@music163/tango-context';
import { wrapCode } from '@music163/tango-helpers';
import { value2code } from '@music163/tango-core';

const wrapperStyle = css`
  .ant-select,
  .ant-input {
    width: 100%;
    margin-bottom: 8px;
  }
`;

export type VueDataBindingSetterProps = FormItemComponentProps<string>;

/**
 * Vue数据绑定设置器
 * 支持选择script中定义的变量，或创建新变量
 */
export function VueDataBindingSetter(props: VueDataBindingSetterProps) {
  const { value, onChange } = props;
  const [bindingType, setBindingType] = useState<'variable' | 'expression' | 'static'>('variable');
  const [selectedVariable, setSelectedVariable] = useState('');
  const [expressionValue, setExpressionValue] = useState('');
  const [staticValue, setStaticValue] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  const workspace = useWorkspace();
  const activeFile = workspace.activeViewModule;

  const code = value2code(value);

  const handleChange = useCallback<FormItemComponentProps['onChange']>(
    (nextValue: any, ...args) => {
      if (!nextValue) {
        onChange(undefined);
      }
      if (nextValue !== code) {
        onChange(wrapCode(nextValue), ...args);
      }
    },
    [onChange, code],
  );

  // 获取可用的数据绑定变量
  const dataBindingVariables = useMemo(() => {
    if (activeFile && typeof activeFile.getDataBindingVariables === 'function') {
      return activeFile.getDataBindingVariables();
    }
    return [];
  }, [activeFile]);

  const bindingTypeOptions = [
    { label: '绑定变量', value: 'variable' },
    { label: 'JS表达式', value: 'expression' },
    { label: '静态值', value: 'static' },
  ];

  const handleBindingTypeChange = (type: 'variable' | 'expression' | 'static') => {
    setBindingType(type);
    
    if (type === 'static') {
      handleChange(staticValue);
    } else if (type === 'variable' && selectedVariable) {
      handleChange(selectedVariable);
    } else if (type === 'expression' && expressionValue) {
      handleChange(`{{ ${expressionValue} }}`);
    }
  };

  const handleVariableSelect = (variableName: string) => {
    setSelectedVariable(variableName);
    if (bindingType === 'variable') {
      handleChange(variableName);
    }
  };

  const handleExpressionChange = (expr: string) => {
    setExpressionValue(expr);
    if (bindingType === 'expression') {
      handleChange(`{{ ${expr} }}`);
    }
  };

  const handleStaticValueChange = (val: string) => {
    setStaticValue(val);
    if (bindingType === 'static') {
      handleChange(val);
    }
  };

  const handleCreateVariable = () => {
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then((values) => {
      const { variableName, variableType, initialValue } = values;
      
      if (activeFile && typeof activeFile.addScriptVariable === 'function') {
        activeFile.addScriptVariable(variableName, variableType, initialValue);
        
        // 选择新创建的变量
        setSelectedVariable(variableName);
        if (bindingType === 'variable') {
          handleChange(variableName);
        }
      }
      
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <Box css={wrapperStyle}>
      <Select
        placeholder="选择绑定类型"
        value={bindingType}
        onChange={handleBindingTypeChange}
        options={bindingTypeOptions}
      />
      
      {bindingType === 'variable' && (
        <Space.Compact style={{ width: '100%' }}>
          <Select
            placeholder="选择变量"
            value={selectedVariable}
            onChange={handleVariableSelect}
            options={dataBindingVariables}
            style={{ flex: 1 }}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
          <Button icon={<PlusOutlined />} onClick={handleCreateVariable} />
        </Space.Compact>
      )}
      
      {bindingType === 'expression' && (
        <Input.TextArea
          placeholder="输入JS表达式，如：user.name + ' - ' + user.role"
          value={expressionValue}
          onChange={(e) => handleExpressionChange(e.target.value)}
          rows={3}
        />
      )}
      
      {bindingType === 'static' && (
        <Input
          placeholder="输入静态值"
          value={staticValue}
          onChange={(e) => handleStaticValueChange(e.target.value)}
        />
      )}
      
      <Box mt="s">
        <Text fontSize="12px" color="text.placeholder">
          {bindingType === 'variable' && '绑定script中定义的响应式变量'}
          {bindingType === 'expression' && '使用JS表达式进行动态计算'}
          {bindingType === 'static' && '设置固定的静态值'}
        </Text>
      </Box>

      <Modal
        title="创建新变量"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={400}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="variableName"
            label="变量名"
            rules={[
              { required: true, message: '请输入变量名' },
              { pattern: /^[a-zA-Z_$][a-zA-Z0-9_$]*$/, message: '变量名格式不正确' },
            ]}
          >
            <Input placeholder="如：tableData" />
          </Form.Item>
          
          <Form.Item
            name="variableType"
            label="变量类型"
            rules={[{ required: true, message: '请选择变量类型' }]}
            initialValue="ref"
          >
            <Select
              options={[
                { label: 'ref (响应式引用)', value: 'ref' },
                { label: 'reactive (响应式对象)', value: 'reactive' },
                { label: 'const (常量)', value: 'const' },
                { label: 'let (变量)', value: 'let' },
              ]}
            />
          </Form.Item>
          
          <Form.Item
            name="initialValue"
            label="初始值"
            tooltip="可选，留空则使用默认值"
          >
            <Input.TextArea
              placeholder="如：[] 或 {} 或 'hello'"
              rows={2}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Box>
  );
}

/**
 * Vue方法绑定设置器
 * 支持选择script中定义的方法
 */
export function VueMethodBindingSetter(props: VueDataBindingSetterProps) {
  const { value, onChange } = props;
  const [selectedMethod, setSelectedMethod] = useState('');
  const [customExpression, setCustomExpression] = useState('');
  const [bindingType, setBindingType] = useState<'method' | 'expression'>('method');
  
  const workspace = useWorkspace();
  const activeFile = workspace.activeViewModule;

  const code = value2code(value);

  const handleChange = useCallback<FormItemComponentProps['onChange']>(
    (nextValue: any, ...args) => {
      if (!nextValue) {
        onChange(undefined);
      }
      if (nextValue !== code) {
        onChange(wrapCode(nextValue), ...args);
      }
    },
    [onChange, code],
  );

  // 获取可用的事件绑定方法
  const eventBindingMethods = useMemo(() => {
    if (activeFile && typeof activeFile.getEventBindingMethods === 'function') {
      return activeFile.getEventBindingMethods();
    }
    return [];
  }, [activeFile]);

  const bindingTypeOptions = [
    { label: '选择方法', value: 'method' },
    { label: '自定义表达式', value: 'expression' },
  ];

  const handleBindingTypeChange = (type: 'method' | 'expression') => {
    setBindingType(type);
    
    if (type === 'method' && selectedMethod) {
      handleChange(`${selectedMethod}()`);
    } else if (type === 'expression' && customExpression) {
      handleChange(customExpression);
    }
  };

  const handleMethodSelect = (methodName: string) => {
    setSelectedMethod(methodName);
    if (bindingType === 'method') {
      handleChange(`${methodName}()`);
    }
  };

  const handleExpressionChange = (expr: string) => {
    setCustomExpression(expr);
    if (bindingType === 'expression') {
      handleChange(expr);
    }
  };

  return (
    <Box css={wrapperStyle}>
      <Select
        placeholder="选择绑定类型"
        value={bindingType}
        onChange={handleBindingTypeChange}
        options={bindingTypeOptions}
      />
      
      {bindingType === 'method' && (
        <Select
          placeholder="选择方法"
          value={selectedMethod}
          onChange={handleMethodSelect}
          options={eventBindingMethods}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
        />
      )}
      
      {bindingType === 'expression' && (
        <Input.TextArea
          placeholder="输入自定义表达式，如：handleClick($event)"
          value={customExpression}
          onChange={(e) => handleExpressionChange(e.target.value)}
          rows={2}
        />
      )}
      
      <Box mt="s">
        <Text fontSize="12px" color="text.placeholder">
          {bindingType === 'method' && '选择script中定义的方法'}
          {bindingType === 'expression' && '输入自定义的方法调用表达式'}
        </Text>
      </Box>
    </Box>
  );
}
