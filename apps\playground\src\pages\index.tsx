import { Box } from 'coral-system';
import { Button, Form, Input, Modal, Radio, Space } from 'antd';
import {
  Designer,
  DesignerPanel,
  SettingPanel,
  Sidebar,
  Toolbar,
  WorkspacePanel,
  WorkspaceView,
  CodeEditor,
  Sandbox,
  DndQuery,
  themeLight,
} from '@music163/tango-designer';
import { createEngine, Workspace } from '@music163/tango-core';
import {
  Logo,
  ProjectDetail,
  bootHelperVariables,
  vueEmptyPageCode,
  vueListPageCode,
} from '../helpers';
import nubesUiPrototypes from '../helpers/nubes-ui-prototypes';
import {
  ApiOutlined,
  AppstoreAddOutlined,
  BuildOutlined,
  FunctionOutlined,
  PlusOutlined,
  createFromIconfontCN,
} from '@ant-design/icons';
import { Action, PackageOutlined } from '@music163/tango-ui';
import { useState } from 'react';
import EmptyPageIcon from '../assets/empty-page.svg';
import ListPageIcon from '../assets/list-page.svg';

const requestProjectResponse: any = await fetch('/api/detail?id=63170&branch=master');
const vueSampleFiles: any = await requestProjectResponse.json();
let menuData: any;
let workspace: any;
let engine: any;
let sandboxQuery: any;

function createWorkspace() {
  menuData = {
    common: [
      {
        title: '通用类',
        items: [
          'nb-text',
          'nb-button',
          'nb-link',
        ],
      },
      {
        title: '数据展示',
        items: [
          'nb-table',
          'nb-card',
          'nb-empty',
        ],
      },
      {
        title: '表单组件',
        items: [
          'nb-form',
          'nb-form-item',
          'nb-input',
          'nb-date-picker',
          'nb-time-picker',
          'nb-switch',
          'nb-checkbox',
        ],
      },
      {
        title: '反馈组件',
        items: [
          'nb-dialog',
        ],
      },
    ],
  };

  // 1. 实例化工作区
  workspace = new Workspace({
    projectType: 'vue3',
    entry: '/src/main.js',
    files: vueSampleFiles,
    prototypes: nubesUiPrototypes,
  });

  // inject workspace to window for debug
  (window as any).__workspace__ = workspace;

  // 2. 引擎初始化
  engine = createEngine({
    workspace,
    menuData,
    defaultActiveView: 'design',
  });

  // 3. 沙箱初始化
  sandboxQuery = new DndQuery({
    context: 'iframe',
  });

  // 4. 图标库初始化（物料面板和组件树使用了 iconfont 里的图标）
  createFromIconfontCN({
    scriptUrl: '//at.alicdn.com/t/c/font_2891794_151xsllxqd7.js',
  });
}

createWorkspace();

// @ts-ignore
window.__workspace__ = workspace;
console.log('workspace:', workspace);

const initFiles: any = {}
for (let [key, value] of workspace?.files?.data_) {
  // if (key.indexOf('.vue') > -1) {
    initFiles[key] = {
      code: value.value_._code,
      path: key
    }    
  // }

}
await fetch("/api/updateSandbox", {
  "headers": {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
  },
  "body": JSON.stringify({
    modules: initFiles,
    id: new URL(document.location).searchParams.get("id")
  }),
  "method": "POST",
  "mode": "cors",
});

/**
 * 5. 平台初始化，访问 https://code-hive-dev.wanyol.com:6006/
 */
export default function App() {
  const [showNewPageModal, setShowNewPageModal] = useState(false);
  const [form] = Form.useForm();
  return (
    <Designer
      theme={themeLight}
      engine={engine}
      config={{
        customActionVariables: bootHelperVariables,
        customExpressionVariables: bootHelperVariables,
      }}
      sandboxQuery={sandboxQuery}
    >
      <DesignerPanel
        logo={<Logo />}
        description={<ProjectDetail />}
        actions={
          <Box px="l">
            <Toolbar>
              <Toolbar.Item key="routeSwitch" placement="left" activeViews={['design']} />
              <Toolbar.Item key="addPage" placement="left" activeViews={['design']}>
                <Action
                  tooltip="添加页面"
                  shape="outline"
                  icon={<PlusOutlined />}
                  onClick={() => setShowNewPageModal(true)}
                />
              </Toolbar.Item>
              <Toolbar.Item key="history" placement="left" activeViews={['design']} />
              <Toolbar.Item key="preview" placement="left" activeViews={['design']} />
              <Toolbar.Item key="togglePanel" placement="right" activeViews={['design']} />
              <Toolbar.Item key="modeSwitch" placement="right" />
              <Toolbar.Separator />
            </Toolbar>
            <Modal
              title="添加新页面"
              open={showNewPageModal}
              onCancel={() => setShowNewPageModal(false)}
              footer={null}
            >
              <Form
                form={form}
                onFinish={(values) => {
                  if (values.template === 'listPage') {
                    workspace.addViewFile(values.name, vueListPageCode);
                  } else {
                    workspace.addViewFile(values.name, vueEmptyPageCode);
                  }
                  setShowNewPageModal(false);
                }}
                layout="vertical"
              >
                <Form.Item label="文件名" name="name" required rules={[{ required: true }]}>
                  <Input placeholder="请输入文件名" />
                </Form.Item>
                <Form.Item label="页面模板" name="template">
                  <Radio.Group defaultValue="emptyPage">
                    <Radio value="emptyPage">
                      空页面
                      <br />
                      <img 
                        src={EmptyPageIcon} 
                        alt="空页面预览" 
                        style={{ width: '150px', height: 'auto' }} 
                      />
                    </Radio>
                    <Radio value="listPage">
                      中后台列表页
                      <br />
                      <img
                        src={ListPageIcon}
                        alt="列表页预览"
                        style={{ width: '150px', height: 'auto' }}
                      />
                    </Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    提交
                  </Button>
                </Form.Item>
              </Form>
            </Modal>
          </Box>
        }
      >
        <Sidebar>
          <Sidebar.Item
            key="components"
            label="组件"
            icon={<AppstoreAddOutlined />}
            widgetProps={{
              menuData,
            }}
          />
          <Sidebar.Item key="outline" label="结构" icon={<BuildOutlined />} />
          {/* <Sidebar.Item
            key="variables"
            label="变量"
            icon={<FunctionOutlined />}
            isFloat
            width={800}
          />
          <Sidebar.Item key="dataSource" label="接口" icon={<ApiOutlined />} isFloat width={800} /> */}
          <Sidebar.Item
            key="dependency"
            label="依赖"
            icon={<PackageOutlined />}
            isFloat
            width={800}
          />
        </Sidebar>
        <WorkspacePanel>
          <WorkspaceView mode="code">
            <CodeEditor />
          </WorkspaceView>
          <WorkspaceView mode="design">
            <Sandbox
              // bundlerURL={sandboxLink}
              onMessage={(e) => {
                if (e.type === 'done') {
                  const sandboxWindow: any = sandboxQuery.window;
                  // if (sandboxWindow.TangoAntd) {
                  // if (sandboxWindow.TangoAntd.menuData) {
                  //   setMenuData(sandboxWindow.TangoAntd.menuData);
                  // }
                  // if (sandboxWindow.TangoAntd.prototypes) {
                  //   workspace.setComponentPrototypes(sandboxWindow.TangoAntd.prototypes);
                  // }
                  // }
                  if (sandboxWindow.localTangoComponentPrototypes) {
                    workspace.setComponentPrototypes(sandboxWindow.localTangoComponentPrototypes);
                  }
                }
              }}
              // navigatorExtra={<Button size="small">hello world</Button>}
            />
          </WorkspaceView>
        </WorkspacePanel>
        <SettingPanel />
      </DesignerPanel>
    </Designer>
  );
}
