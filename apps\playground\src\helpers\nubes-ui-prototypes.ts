import type { IComponentPrototype, Dict } from '@music163/tango-helpers';

const nubesUiPrototypes: Dict<IComponentPrototype> = {
  /**
   * 通用类
   */
  'nb-text': {
    title: '文本',
    name: 'nb-text',
    exportType: 'namedExport',
    icon: 'icon-wenzi',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '添加文字内容。',
    hasChildren: true,
    initChildren: `文本`,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'data-innertext',
        title: '文本内容',
        setter: 'textSetter',
      },
      {
        name: 'type',
        title: '文本类型',
        setter: 'pickerSetter',
        options: [
          { label: '主要', value: 'primary' },
          { label: '成功', value: 'success' },
          { label: '警告', value: 'warning' },
          { label: '危险', value: 'danger' },
          { label: '提示', value: 'info' },
        ],
      },
    ],
  },
  'nb-button': {
    title: '按钮',
    name: 'nb-button',
    exportType: 'namedExport',
    icon: 'icon-anniu',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '常用的操作按钮。',
    hasChildren: true,
    initChildren: `按钮`,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'data-innertext',
        title: '按钮文本',
        setter: 'textSetter',
      },
      {
        name: 'type',
        title: '按钮类型',
        setter: 'pickerSetter',
        options: [
          { label: '主要', value: 'primary' },
          { label: '成功', value: 'success' },
          { label: '警告', value: 'warning' },
          { label: '危险', value: 'danger' },
          { label: '提示', value: 'info' },
          { label: '文本', value: 'text' },
        ],
      },
      {
        name: 'round',
        title: '圆角按钮',
        setter: 'boolSetter',
      },
      {
        name: '@click',
        title: '点击事件',
        setter: 'vueEventSetter',
        group: 'event',
      }
    ],
  },
  'nb-link': {
    title: '链接',
    name: 'nb-link',
    exportType: 'namedExport',
    icon: 'icon-link-button',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '文字超链接。',
    hasChildren: true,
    initChildren: `超链接`,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'href',
        title: '链接地址',
        setter: 'textSetter',
      },
      {
        name: 'data-innertext',
        title: '链接文本',
        setter: 'textSetter',
      },
      {
        name: 'type',
        title: '类型',
        setter: 'pickerSetter',
        options: [
          { label: '默认', value: 'default' },
          { label: '主要', value: 'primary' },
          { label: '成功', value: 'success' },
          { label: '警告', value: 'warning' },
          { label: '危险', value: 'danger' },
          { label: '提示', value: 'info' },
        ],
      },
      {
        name: 'target',
        title: '打开方式',
        setter: 'pickerSetter',
        options: [
          { label: '新窗口打开', value: '_blank' },
          { label: '当前窗口打开', value: '_self' },
          { label: '父级窗口打开', value: '_parent' },
        ],
      },
    ],
  },
  /**
   * 数据展示组件
   */
  'nb-table': {
    title: '表格',
    name: 'nb-table',
    exportType: 'namedExport',
    icon: 'icon-biaoge',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '用于展示多条结构类似的数据， 可对数据进行排序、筛选、对比或其他自定义操作。',
    hasChildren: true,
    childrenName: ['nb-table-column'],
    initChildren: `
      <nb-table-column prop="column1" label="column1" width="180" />
      <nb-table-column prop="column2" label="column2" width="180" />
      <nb-table-column prop="column3" label="column3" />
    `,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: ':data',
        title: '表数据',
        setter: 'vueDataBindingSetter',
        group: 'basic',
      },
      {
        name: 'stripe',
        title: '斑马纹效果',
        setter: 'boolSetter',
      },
    ],
  },
  'nb-card': {
    title: '卡片',
    name: 'nb-card',
    exportType: 'namedExport',
    icon: 'icon-card',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '将信息聚合在卡片容器中展示。',
    hasChildren: true,
    initChildren: `
      <span>这里是卡片的内容</span>
    `,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'header',
        title: '卡片标题',
        setter: 'textSetter',
      },
      {
        name: 'footer',
        title: '卡片页脚',
        setter: 'textSetter',
      },
    ],
  },
  'nb-empty': {
    title: '空状态',
    name: 'nb-empty',
    exportType: 'namedExport',
    icon: 'icon-kongzhuangtai',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '空状态时的占位提示。',
    hasChildren: false,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'description',
        title: '描述信息',
        setter: 'textSetter',
      },
    ],
  },
  /**
   * 反馈组件
   */
  'nb-dialog': {
    title: '对话框',
    name: 'nb-dialog',
    exportType: 'namedExport',
    icon: 'icon-duihuakuang',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '在保留当前页面状态的情况下，告知用户并承载相关操作。',
    hasChildren: true,
    initChildren: `
      <template #header>
        <span>对话框标题</span>
      </template>
      <span>这里是对话框的内容</span>
      <template #footer>
        <nb-button @click="dialogVisible = false">取消</nb-button>
        <nb-button type="primary" @click="dialogVisible = false">确定</nb-button>
      </template>
    `,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
        initValue: 'dialog',
        autoInitValue: true,
      },
      {
        name: 'v-model',
        title: '是否显示对话框',
        setter: 'vueBoolSetter',
        group: 'basic',
        initValue: 'dialogVisible',
      },
      {
        name: 'title',
        title: '对话框标题',
        setter: 'textSetter',
        group: 'basic',
      },
      {
        name: 'width',
        title: '对话框宽度',
        setter: 'textSetter',
        group: 'basic',
      },
      {
        name: 'fullscreen',
        title: '是否全屏显示',
        setter: 'boolSetter',
        group: 'basic',
      },
      {
        name: 'top',
        title: '距离顶部的距离',
        setter: 'textSetter',
        group: 'style',
      },
      {
        name: 'modal',
        title: '是否需要遮罩层',
        setter: 'boolSetter',
        group: 'basic',
      },
      {
        name: 'append-to-body',
        title: '插入至body元素',
        setter: 'boolSetter',
        group: 'advanced',
      },
      {
        name: 'lock-scroll',
        title: '锁定body滚动',
        setter: 'boolSetter',
        group: 'advanced',
      },
      {
        name: 'close-on-click-modal',
        title: '点击遮罩关闭',
        setter: 'boolSetter',
        group: 'advanced',
      },
      {
        name: 'close-on-press-escape',
        title: '按ESC关闭',
        setter: 'boolSetter',
        group: 'advanced',
      },
      {
        name: 'show-close',
        title: '显示关闭按钮',
        setter: 'boolSetter',
        group: 'basic',
      },
      {
        name: 'center',
        title: '头部和底部居中',
        setter: 'boolSetter',
        group: 'style',
      },
      {
        name: 'align-center',
        title: '水平垂直对齐',
        setter: 'boolSetter',
        group: 'style',
      },
      {
        name: 'draggable',
        title: '启用拖拽功能',
        setter: 'boolSetter',
        group: 'advanced',
      },
      {
        name: 'destroy-on-close',
        title: '关闭时销毁元素',
        setter: 'boolSetter',
        group: 'advanced',
      },
      {
        name: 'open-delay',
        title: '打开延时(毫秒)',
        setter: 'numberSetter',
        group: 'advanced',
      },
      {
        name: 'close-delay',
        title: '关闭延时(毫秒)',
        setter: 'numberSetter',
        group: 'advanced',
      },
      {
        name: '@open',
        title: '打开回调',
        setter: 'vueEventSetter',
        group: 'event',
      },
      {
        name: '@close',
        title: '关闭回调',
        setter: 'vueEventSetter',
        group: 'event',
      },
      {
        name: '@opened',
        title: '打开动画结束回调',
        setter: 'vueEventSetter',
        group: 'event',
      },
      {
        name: '@closed',
        title: '关闭动画结束回调',
        setter: 'vueEventSetter',
        group: 'event',
      },
    ],
  },
  /**
   * 表单组件
   */
  'nb-form': {
    title: '表单',
    name: 'nb-form',
    exportType: 'namedExport',
    icon: 'icon-biaodan',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '表单包含 输入框, 单选框, 下拉选择, 多选框 等用户输入的组件。 使用表单，您可以收集、验证和提交数据。',
    hasChildren: true,
    childrenName: ['nb-form-item'],
    initChildren: `
      <nb-form-item label="Activity name">
        <nb-input />
      </nb-form-item>
      <nb-form-item label="Activity time">
        <nb-date-picker
          type="date"
          placeholder="Pick a date"
          style="width: 100%"
        />
      </nb-form-item>
      <nb-form-item label="Instant delivery">
        <nb-switch />
      </nb-form-item>
      <nb-form-item label="Activity type">
        <nb-checkbox-group>
          <nb-checkbox 
            label="Online activities" 
            value="Online activities" 
            name="type"
          />
          <nb-checkbox 
            label="Promotion activities"
            value="Promotion activities" 
            name="type" 
          />
          <nb-checkbox 
            label="Offline activities" 
            value="Offline activities" 
            name="type" 
          />
          <nb-checkbox 
            label="Simple brand exposure" 
            value="Simple brand exposure" 
            name="type"
          />
        </nb-checkbox-group>
      </nb-form-item>
      <nb-form-item label="Activity form">
        <nb-input type="textarea" />
      </nb-form-item>
      <nb-form-item>
        <nb-button type="primary" @click="onSubmit">Create</nb-button>
        <nb-button>Cancel</nb-button>
      </nb-form-item>
    `,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: ':model',
        title: '绑定的表单数据对象',
        setter: 'vueDataBindingSetter',
      },
      {
        name: 'label-width',
        title: '标签的长度',
        setter: 'numberSetter',
      },
      {
        name: 'size',
        title: '表单内组件的尺寸',
        setter: 'choiceSetter',
        setterProps: {
          options: [
            { label: '默认', value: 'default' },
            { label: '大', value: 'large' },
            { label: '小', value: 'small' },
          ],
        },
      },
    ],
  },
  'nb-form-item': {
    title: '表单项',
    name: 'nb-form-item',
    exportType: 'namedExport',
    icon: 'icon-biaodan',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '一个基本的表单项。',
    hasChildren: true,
    initChildren: `
      <nb-input />
    `,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'label',
        title: '表单项的标签文本',
        setter: 'textSetter',
      },
    ],
  },
  'nb-input': {
    title: '输入框',
    name: 'nb-input',
    exportType: 'namedExport',
    icon: 'icon-shurukuang',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '通过鼠标或键盘输入字符。',
    hasChildren: false,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'placeholder',
        title: '占位符',
        setter: 'textSetter',
      },
      {
        name: 'v-model',
        title: '绑定值',
        setter: 'vueDataBindingSetter',
        group: 'basic',
      },
      {
        name: 'clearable',
        title: '是否显示清除按钮',
        setter: 'boolSetter',
      },
    ],
  },
  'nb-date-picker': {
    title: '日期选择器',
    name: 'nb-date-picker',
    exportType: 'namedExport',
    icon: 'icon-riqishijian',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '用于选择或输入日期。',
    hasChildren: false,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'placeholder',
        title: '非范围选择时的占位内容',
        setter: 'textSetter',
      },
      {
        name: 'clearable',
        title: '是否显示清除按钮',
        setter: 'boolSetter',
      },
    ],
  },
  'nb-time-picker': {
    title: '时间选择器',
    name: 'nb-time-picker',
    exportType: 'namedExport',
    icon: 'icon-shijianxuanze',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '用于选择或输入日期。',
    hasChildren: false,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'placeholder',
        title: '非范围选择时的占位内容',
        setter: 'textSetter',
      },
      {
        name: 'clearable',
        title: '是否显示清除按钮',
        setter: 'boolSetter',
      },
    ],
  },
  'nb-switch': {
    title: '开关',
    name: 'nb-switch',
    exportType: 'namedExport',
    icon: 'icon-kaiguan',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '表示两种相互对立的状态间的切换，多用于触发「开/关」。',
    hasChildren: false,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'size',
        title: 'switch 的大小',
        setter: 'choiceSetter',
        setterProps: {
          options: [
            { label: '默认', value: 'default' },
            { label: '大', value: 'large' },
            { label: '小', value: 'small' },
          ],
        },
      },
      {
        name: 'active-text',
        title: 'switch 打开时的文字描述',
        setter: 'textSetter',
      },
      {
        name: 'inactive-text',
        title: 'switch 的状态为 off 时的文字描述',
        setter: 'textSetter',
      },
    ],
  },
  'nb-checkbox': {
    title: '多选框',
    name: 'nb-checkbox',
    exportType: 'namedExport',
    icon: 'icon-duoxuankuang',
    type: 'element',
    package: '@oplus/nubes-ui',
    help: '在一组备选项中进行多选。',
    hasChildren: false,
    props: [
      {
        name: 'id',
        title: 'ID',
        setter: 'textSetter',
      },
      {
        name: 'label',
        title: '选中状态的文本描述',
        setter: 'textSetter',
      },
      {
        name: 'value',
        title: '选中状态的值',
        setter: 'textSetter',
      },
      {
        name: 'border',
        title: '是否显示边框',
        setter: 'boolSetter',
      }
    ],
  },
};

const nativeDomPrototypes = () => {
  const doms = [
    'div',
    'span',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'p',
    'a',
    'img',
    'ul',
    'ol',
    'li',
    'input',
    'button',
    'form',
    'table',
    'tr',
    'td',
    'header',
    'footer',
    'nav',
    'section',
    'article',
    'aside',
    'main',
    'video',
    'audio',
    'label',
    'select',
    'option',
    'textarea',
    'iframe',
  ];
  const componentPrototypes: Dict<IComponentPrototype> = doms.reduce(
    (acc: Dict<IComponentPrototype>, tag) => {
      acc[tag] = {
        name: tag,
        title: tag,
        hasChildren: true,
        package: '',
        type: 'element',
        props: [
          {
            name: 'style',
            title: '样式',
            group: 'style',
            setter: 'expressionSetter',
            setterProps: {
              expressionType: 'cssObject',
            },
          },
          {
            name: 'className',
            title: '类名',
            setter: 'classNameSetter',
          },
          {
            name: 'id',
            title: 'ID',
            setter: 'textSetter',
          },
          {
            name: '@click',
            title: '点击事件',
            setter: 'vueEventSetter',
            group: 'event',
          },
        ],
      };
      return acc;
    },
    {},
  );

  return componentPrototypes;
};

export default {
  ...nativeDomPrototypes(),
  ...nubesUiPrototypes
};
