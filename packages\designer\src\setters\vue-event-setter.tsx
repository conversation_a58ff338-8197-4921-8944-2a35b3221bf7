import React, { useCallback, useMemo, useState } from 'react';
import { css, Box, Text } from 'coral-system';
import { AutoComplete, Input } from 'antd';
import { ActionSelect } from '@music163/tango-ui';
import { FormItemComponentProps } from '@music163/tango-setting-form';
import { useWorkspace, useWorkspaceData } from '@music163/tango-context';
import { Dict, wrapCode } from '@music163/tango-helpers';
import { ExpressionPopover, getCallbackValue } from './code-setter';
import { value2code } from '@music163/tango-core';

enum VueEventAction {
  NoAction = 'noAction',
  ConsoleLog = 'consoleLog',
  BindExpression = 'bindExpression',
  OpenModal = 'openModal',
  CloseModal = 'closeModal',
  NavigateTo = 'navigateTo',
  ToggleVariable = 'toggleVariable',
  SetVariable = 'setVariable',
  CallMethod = 'callMethod',
}

const wrapperStyle = css`
  .ant-select,
  .ant-input {
    width: 100%;
    margin-bottom: 8px;
  }
`;

export type VueEventSetterProps = FormItemComponentProps<string>;

/**
 * Vue事件监听函数绑定器
 */
export function VueEventSetter(props: VueEventSetterProps) {
  const { value, onChange, modalTitle, modalTip, template } = props;
  const [type, setType] = useState<VueEventAction>(); // 事件类型
  const [temp, setTemp] = useState(''); // 二级暂存值
  const { actionVariables, routeOptions } = useWorkspaceData();
  const workspace = useWorkspace();
  const modalOptions = workspace.activeViewModule.listModals() || [];

  const code = value2code(value);

  const handleChange = useCallback<FormItemComponentProps['onChange']>(
    (nextValue: any, ...args) => {
      if (!nextValue) {
        onChange(undefined);
      }
      if (nextValue !== code) {
        onChange(wrapCode(nextValue), ...args);
      }
    },
    [onChange, code],
  );

  const options = useMemo(
    () => [
      { label: '无动作', value: VueEventAction.NoAction },
      { label: '打印事件', value: VueEventAction.ConsoleLog },
      {
        label: (
          <ExpressionPopover
            title={modalTitle}
            subTitle={modalTip}
            value={value}
            template={template}
            onOk={(nextValue) => {
              handleChange(nextValue);
            }}
            dataSource={actionVariables}
          >
            <Text>绑定 JS 表达式</Text>
          </ExpressionPopover>
        ),
        value: VueEventAction.BindExpression,
      },
      { label: '打开页面', value: VueEventAction.NavigateTo },
      { label: '打开弹窗', value: VueEventAction.OpenModal },
      { label: '关闭弹窗', value: VueEventAction.CloseModal },
      { label: '切换变量值', value: VueEventAction.ToggleVariable },
      { label: '设置变量值', value: VueEventAction.SetVariable },
      { label: '调用方法', value: VueEventAction.CallMethod },
    ],
    [modalTitle, value, actionVariables, template, handleChange],
  );

  const onAction = (actionType: VueEventAction) => {
    setType(actionType);
    if (actionType === VueEventAction.NoAction) {
      handleChange(undefined);
    }
  };

  return (
    <Box css={wrapperStyle}>
      <ActionSelect options={options} onSelect={onAction} defaultText="请选择动作类型" />
      {type === VueEventAction.ConsoleLog && (
        <Input
          placeholder="输入 Console.log 日志内容"
          value={temp}
          onChange={(e) => setTemp(e.target.value)}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
      {type === VueEventAction.NavigateTo && (
        <AutoComplete
          placeholder="选择或输入页面路由"
          options={routeOptions}
          value={temp}
          onChange={setTemp}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
      {type === VueEventAction.OpenModal && (
        <AutoComplete
          placeholder="选择或输入弹窗 id"
          options={modalOptions}
          value={temp}
          onChange={setTemp}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
      {type === VueEventAction.CloseModal && (
        <AutoComplete
          placeholder="选择或输入弹窗 id"
          options={modalOptions}
          value={temp}
          onChange={setTemp}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
      {type === VueEventAction.ToggleVariable && (
        <Input
          placeholder="输入变量名，如：dialogVisible"
          value={temp}
          onChange={(e) => setTemp(e.target.value)}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
      {type === VueEventAction.SetVariable && (
        <Input
          placeholder="输入变量赋值，如：dialogVisible = true"
          value={temp}
          onChange={(e) => setTemp(e.target.value)}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
      {type === VueEventAction.CallMethod && (
        <Input
          placeholder="输入方法调用，如：handleSubmit()"
          value={temp}
          onChange={(e) => setTemp(e.target.value)}
          onBlur={() => {
            if (temp) {
              handleChange(getVueExpressionValue(type, temp));
            }
          }}
        />
      )}
    </Box>
  );
}

const vueHandlerMap: Dict = {
  [VueEventAction.ConsoleLog]: 'console.log',
  [VueEventAction.OpenModal]: '$refs.{value}.open',
  [VueEventAction.CloseModal]: '$refs.{value}.close',
  [VueEventAction.NavigateTo]: '$router.push',
  [VueEventAction.ToggleVariable]: '{value} = !{value}',
  [VueEventAction.SetVariable]: '{value}',
  [VueEventAction.CallMethod]: '{value}',
};

function getVueExpressionValue(type: VueEventAction, value = '') {
  const handler = vueHandlerMap[type];
  if (handler) {
    if (type === VueEventAction.ConsoleLog) {
      return getCallbackValue(`${handler}("${value}");`);
    } else if (type === VueEventAction.NavigateTo) {
      return getCallbackValue(`${handler}("${value}");`);
    } else if (type === VueEventAction.OpenModal || type === VueEventAction.CloseModal) {
      return getCallbackValue(handler.replace('{value}', value));
    } else {
      return getCallbackValue(handler.replace('{value}', value));
    }
  }
}
