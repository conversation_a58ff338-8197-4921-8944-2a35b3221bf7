import { extendTheme } from 'coral-system';

export default extendTheme({
  colors: {
    custom: {
      topNavBg: '#FFF',
      topNavColor: '#333',
      topNavBorderColor: 'rgb(229,230,235)',
      toolbarDividerColor: 'rgb(229,230,235)',
      toolbarButtonBg: 'rgb(242,243,245)',
      toolbarButtonBgHover: 'rgb(229,230,235)',
      toolbarButtonBgDisabled: 'rgb(247,248,250)',
      toolbarButtonBgActive: 'colors.brand',
      toolbarButtonTextColor: 'colors.text2',
      toolbarButtonTextColorHover: 'colors.text2',
      toolbarButtonTextColorDisabled: 'colors.text4',
      toolbarButtonTextColorActive: '#FFF',
      sidebarBg: '#fff',
      sidebarExpandBg: '#fff',
      sidebarItemActiveBg: '#f2f3f5',
      sidebarItemHoverBg: '#f2f3f5',
      viewportBg: '#f0f2f5',
    },
  },
});
