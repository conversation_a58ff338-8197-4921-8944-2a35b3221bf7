{"name": "playground", "version": "0.0.0", "private": true, "author": "Wells <<EMAIL>>", "scripts": {"dev": "cross-env HOST=code-hive-dev.wanyol.com PORT=8001 umi dev", "build:dev": "cross-env UMI_ENV=dev umi build", "build:test": "cross-env UMI_ENV=test umi build", "build:production": "cross-env UMI_ENV=production umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@music163/antd": "^0.2.4", "antd": "^4.24.2", "coral-system": "^1.0.5", "umi": "^4.2.3"}}