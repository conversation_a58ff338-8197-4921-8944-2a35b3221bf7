/**
 * 环境配置工具
 * 用于根据不同环境动态设置沙箱的基础URL
 */

export type Environment = 'dev' | 'test' | 'production' | 'local';

/**
 * 环境域名映射配置
 */
const ENV_DOMAIN_MAP: Record<Environment, string> = {
  dev: '//code-hive-dev.wanyol.com',
  test: '//code-hive.wanyol.com',
  production: '//code-hive.oppoer.me',
  local: '//code-hive-dev.wanyol.com'
};

/**
 * 获取当前环境
 * 优先级：
 * 1. 环境变量 NODE_ENV 或 UMI_ENV
 * 2. 根据域名判断
 * 3. 默认为 local
 */
export function getCurrentEnvironment(): Environment {
  // 1. 检查环境变量
  if (typeof process !== 'undefined' && process.env) {
    const nodeEnv = process.env.NODE_ENV;
    const umiEnv = process.env.UMI_ENV;
    
    if (nodeEnv === 'development') {
      return 'local';
    }
    if (umiEnv && ['dev', 'test', 'production'].includes(umiEnv)) {
      return umiEnv as Environment;
    }
  }

  // 2. 根据当前域名判断环境
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    if (hostname.includes('code-hive.oppoer.me')) {
      return 'production';
    }
    if (hostname.includes('code-hive-dev.wanyol.com')) {
      return 'dev';
    }
    if (hostname.includes('code-hive.wanyol.com')) {
      return 'test';
    }
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
      return 'local';
    }
  }

  // 3. 默认返回 local
  return 'local';
}

/**
 * 获取沙箱基础URL
 * @param env 可选的环境参数，如果不传则自动检测当前环境
 * @returns 沙箱基础URL
 */
export function getSandboxBaseUrl(env?: Environment): string {
  const currentEnv = env || getCurrentEnvironment();
  return ENV_DOMAIN_MAP[currentEnv];
}

/**
 * 构建完整的沙箱链接
 * @param containerId 容器ID
 * @param env 可选的环境参数
 * @returns 完整的沙箱链接
 */
export function buildSandboxLink(containerId: string, env?: Environment): string {
  const baseUrl = getSandboxBaseUrl(env);
  return `${baseUrl}/api/container/${containerId}/`;
}

/**
 * 获取环境域名映射配置
 * @returns 环境域名映射对象
 */
export function getEnvironmentDomainMap(): Record<Environment, string> {
  return { ...ENV_DOMAIN_MAP };
}

/**
 * 检查是否为指定环境
 * @param targetEnv 目标环境
 * @param currentEnv 当前环境，如果不传则自动检测
 * @returns 是否为指定环境
 */
export function isEnvironment(targetEnv: Environment, currentEnv?: Environment): boolean {
  const env = currentEnv || getCurrentEnvironment();
  return env === targetEnv;
}

/**
 * 检查是否为生产环境
 * @returns 是否为生产环境
 */
export function isProduction(): boolean {
  return isEnvironment('production');
}

/**
 * 检查是否为开发环境
 * @returns 是否为开发环境
 */
export function isDevelopment(): boolean {
  return isEnvironment('dev');
}

/**
 * 检查是否为测试环境
 * @returns 是否为测试环境
 */
export function isTest(): boolean {
  return isEnvironment('test');
}

/**
 * 检查是否为本地环境
 * @returns 是否为本地环境
 */
export function isLocal(): boolean {
  return isEnvironment('local');
}
