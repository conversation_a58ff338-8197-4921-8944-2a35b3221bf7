import { JSXElement } from '@babel/types';
import { Dict } from '@music163/tango-helpers';
import { cloneJSXElement, getJSXElementAttributes, getVueTemplateNodeAttributes } from '../helpers';
import { JsViewFile } from './js-view-file';
import { AbstractViewNode, IViewNodeInitConfig } from './abstract-view-node';
import { VueViewFile } from './vue-view-file';

/**
 * 视图节点类
 */
export class JsxViewNode extends AbstractViewNode<JSXElement, JsViewFile> {
  get loc() {
    return this.rawNode?.loc;
  }

  constructor(props: IViewNodeInitConfig<JSXElement, JsViewFile>) {
    super(props);
    this.props = getJSXElementAttributes(cloneJSXElement(props.rawNode));
  }

  /**
   * 返回克隆后的 ast 节点
   * @param overrideProps 额外设置给克隆节点的属性
   * @returns
   */
  cloneRawNode(overrideProps?: Dict) {
    return cloneJSXElement(this.rawNode, overrideProps);
  }
}

/**
 * Vue视图节点类
 */
export class VueViewNode extends AbstractViewNode<any, VueViewFile> {
  constructor(props: IViewNodeInitConfig<any, VueViewFile>) {
    super(props);
    this.props = getVueTemplateNodeAttributes(props.rawNode);
  }

  get loc() {
    return this.rawNode?.loc;
  }

  /**
   * 返回克隆后的 ast 节点
   * @param overrideProps 额外设置给克隆节点的属性
   * @returns
   */
  cloneRawNode(overrideProps?: Dict) {
    return Object.assign(this.rawNode, overrideProps);
  }
}
