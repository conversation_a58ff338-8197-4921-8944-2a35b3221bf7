{"name": "@music163/tango-context", "version": "1.1.10", "description": "react context for tango-apps", "keywords": ["react", "hooks"], "author": "wwsun <<EMAIL>>", "homepage": "", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/netease/tango.git"}, "scripts": {"clean": "rimraf lib/", "build": "yarn clean && yarn build:esm && yarn build:cjs", "build:esm": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "build:cjs": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "prepublishOnly": "yarn build"}, "peerDependencies": {"react": ">= 16.8"}, "dependencies": {"@music163/tango-core": "^1.4.4", "@music163/tango-helpers": "^1.2.4", "mobx-react-lite": "4.0.7"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}