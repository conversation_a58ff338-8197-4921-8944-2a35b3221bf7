/**
 * 环境配置测试文件
 */

import { 
  getCurrentEnvironment, 
  getSandboxBaseUrl, 
  buildSandboxLink,
  getEnvironmentDomainMap,
  isEnvironment,
  isProduction,
  isDevelopment,
  isTest,
  isLocal
} from '../src/helpers';

// Mock window.location
const mockLocation = (hostname: string, search: string = '') => {
  Object.defineProperty(window, 'location', {
    value: {
      hostname,
      search,
    },
    writable: true,
  });
};

// Mock process.env
const mockProcessEnv = (env: Record<string, string>) => {
  const originalEnv = process.env;
  process.env = { ...originalEnv, ...env };
  return () => {
    process.env = originalEnv;
  };
};

describe('环境配置测试', () => {
  beforeEach(() => {
    // 清理环境变量
    delete process.env.NODE_ENV;
    delete process.env.UMI_ENV;
  });

  describe('getCurrentEnvironment', () => {
    test('应该根据 NODE_ENV=development 返回 local', () => {
      const restore = mockProcessEnv({ NODE_ENV: 'development' });
      expect(getCurrentEnvironment()).toBe('local');
      restore();
    });

    test('应该根据 UMI_ENV 返回对应环境', () => {
      const restore = mockProcessEnv({ UMI_ENV: 'test' });
      expect(getCurrentEnvironment()).toBe('test');
      restore();
    });

    test('应该根据域名判断生产环境', () => {
      mockLocation('code-hive.oppoer.me');
      expect(getCurrentEnvironment()).toBe('production');
    });

    test('应该根据域名判断开发环境', () => {
      mockLocation('code-hive-dev.wanyol.com');
      expect(getCurrentEnvironment()).toBe('dev');
    });

    test('应该根据域名判断测试环境', () => {
      mockLocation('code-hive.wanyol.com');
      expect(getCurrentEnvironment()).toBe('test');
    });

    test('应该根据 localhost 判断本地环境', () => {
      mockLocation('localhost');
      expect(getCurrentEnvironment()).toBe('local');
    });

    test('默认应该返回 local', () => {
      mockLocation('unknown.com');
      expect(getCurrentEnvironment()).toBe('local');
    });
  });

  describe('getSandboxBaseUrl', () => {
    test('应该返回正确的开发环境 URL', () => {
      expect(getSandboxBaseUrl('dev')).toBe('//code-hive-dev.wanyol.com');
    });

    test('应该返回正确的测试环境 URL', () => {
      expect(getSandboxBaseUrl('test')).toBe('//code-hive.wanyol.com');
    });

    test('应该返回正确的生产环境 URL', () => {
      expect(getSandboxBaseUrl('production')).toBe('//code-hive.oppoer.me');
    });

    test('应该返回正确的本地环境 URL', () => {
      expect(getSandboxBaseUrl('local')).toBe('//code-hive-dev.wanyol.com');
    });
  });

  describe('buildSandboxLink', () => {
    test('应该构建正确的沙箱链接', () => {
      const containerId = 'test-container-123';
      expect(buildSandboxLink(containerId, 'dev')).toBe('//code-hive-dev.wanyol.com/api/container/test-container-123/');
      expect(buildSandboxLink(containerId, 'test')).toBe('//code-hive.wanyol.com/api/container/test-container-123/');
      expect(buildSandboxLink(containerId, 'production')).toBe('//code-hive.oppoer.me/api/container/test-container-123/');
      expect(buildSandboxLink(containerId, 'local')).toBe('//code-hive-dev.wanyol.com/api/container/test-container-123/');
    });
  });

  describe('getEnvironmentDomainMap', () => {
    test('应该返回环境域名映射', () => {
      const domainMap = getEnvironmentDomainMap();
      expect(domainMap).toEqual({
        dev: '//code-hive-dev.wanyol.com',
        test: '//code-hive.wanyol.com',
        production: '//code-hive.oppoer.me',
        local: '//code-hive-dev.wanyol.com'
      });
    });
  });

  describe('环境检查函数', () => {
    test('isEnvironment 应该正确判断环境', () => {
      expect(isEnvironment('dev', 'dev')).toBe(true);
      expect(isEnvironment('dev', 'test')).toBe(false);
    });

    test('isProduction 应该正确判断生产环境', () => {
      const restore = mockProcessEnv({ NODE_ENV: 'production' });
      expect(isProduction()).toBe(true);
      restore();
    });

    test('isDevelopment 应该正确判断开发环境', () => {
      mockLocation('code-hive-dev.wanyol.com');
      expect(isDevelopment()).toBe(true);
    });

    test('isTest 应该正确判断测试环境', () => {
      mockLocation('code-hive.wanyol.com');
      expect(isTest()).toBe(true);
    });

    test('isLocal 应该正确判断本地环境', () => {
      mockLocation('localhost');
      expect(isLocal()).toBe(true);
    });
  });
});
