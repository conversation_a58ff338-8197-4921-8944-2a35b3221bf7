/**
 * 访问，遍历，修改 Vue 的AST
 */
import * as nodeHtmlParser from 'node-html-parser';
import { cloneDeep } from 'lodash-es';
import { SLOT, parseDndId } from '@music163/tango-helpers';
import { IdGenerator } from '../id-generator';

/**
 * 修改.vue文件中的<template>，为其注入追踪属性，用于页面可视化编辑
 * @param vueSfcAst 
 * @param idGenerator 
 */
export function traverseVueTemplate(vueSfcAst: any, idGenerator: IdGenerator) {
  const nodes: any[] = [];
  const cleanAst = cloneDeep(vueSfcAst);
  clearVueSfcAst(cleanAst);
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  injectTrackingDataToVueTemplate(templateRootNode, idGenerator, nodes);

  console.log('templateRootNode', templateRootNode);
  vueSfcAst.template.content = templateRootNode.toString();

  return {
    designerAst: vueSfcAst,
    cleanAst,
    nodes
  };
}

/**
 * 向Vue的<template>节点注入用于可视化开发的追踪属性
 * @param rootNode 
 * @param idGenerator 
 * @param nodeList 
 * @param parent 
 */
function injectTrackingDataToVueTemplate(
  rootNode: any, 
  idGenerator: IdGenerator, 
  nodeList: any[],
  parent?: any
) {
  let trackDnd: string;
  let codeId: string;

  // 先检查当前节点是否为元素节点且 rawTagName 不为空，否则直接跳转到 childNodes 继续遍历
  if (rootNode.nodeType === 1 && rootNode.rawTagName) {
    // 获取组件的追踪属性
    trackDnd = rootNode.getAttribute(SLOT.dnd);
    // 用户代码中的 id 标记
    codeId = rootNode.getAttribute('tid');

    let { component, id } = parseDndId(trackDnd);
    component = component || rootNode.rawTagName;

    // 如果没有 ID，生成组件的追踪 ID 并注入属性
    if (!trackDnd) {
      id = idGenerator.generateId(component, codeId).fullId;
      rootNode.setAttribute(SLOT.dnd, id);
    }

    nodeList.push({
      id,
      codeId,
      parentId: parent?.id,
      parentCodeId: parent?.codeId,
      component,
      rawNode: rootNode
    });
  }

  rootNode.childNodes.forEach((childNode: any) => {
    if (childNode.nodeType === 1 && childNode.rawTagName) {
      injectTrackingDataToVueTemplate(
        childNode,
        idGenerator,
        nodeList,
        { id: trackDnd, codeId } 
      );
    }
  });
}

/**
 * 清除Vue的<template>节点的追踪属性
 * @param rootNode 
 */
function clearTrackingDataFromVueTemplate(rootNode: any) {
  if (rootNode.nodeType === 1 && rootNode.rawTagName) {
    rootNode.removeAttribute(SLOT.dnd);
    // data-innertext 只用于在设计器中为选中元素添加文本内容，转换为代码时要清除 data-innertext 属性
    // rootNode.removeAttribute('data-innertext');
  }

  rootNode.childNodes.forEach((childNode: any) => {
    clearTrackingDataFromVueTemplate(childNode);
  });
}

function clearVueSfcAst(vueSfcAst: any) {
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  clearTrackingDataFromVueTemplate(templateRootNode);
  vueSfcAst.template.content = templateRootNode.toString();
}

/**
 * 收集指定节点的属性及其值，返回一个属性列表用于designer的设置器回显属性值
 * @param node 
 */
export function getVueTemplateNodeAttributes(node: any) {
  const nodeAttributes: Record<string, any> = node?._attrs || {};

  return nodeAttributes;
}

/**
 * 更新VueTemplate中HTML节点的属性
 * @param vueSfcAst 
 * @param nodeId 
 * @param config 
 * @returns 
 */
export function updateVueTemplateNodeAttributes(
  vueSfcAst: any,
  nodeId: string,
  config: Record<string, any>
) {
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  const selectedNode = templateRootNode.querySelector(`[${SLOT.dnd}="${nodeId}"]`);

  if (!selectedNode) {
    return vueSfcAst;
  }

  Object.keys(config).forEach((attrName) => {
    if (
      config[attrName] === undefined ||
      config[attrName] === false
    ) {
      // 删除属性
      selectedNode.removeAttribute(attrName);
    } else if (
      typeof config[attrName] === 'string' &&
      config[attrName].indexOf('{{') > -1 &&
      config[attrName].indexOf('}}') > -1
    ) {
      // 为属性设置变量的情况
      selectedNode.setAttribute(`${attrName}`, config[attrName].slice(2, -2));
    } else if (
      attrName === 'data-innertext'
    ) {
      // data-innertext 用于在设计器中为选中元素添加文本内容
      selectedNode.textContent = config[attrName];
      selectedNode.setAttribute('data-innertext', config[attrName]);
    } else {
      selectedNode.setAttribute(attrName, config[attrName]);
    }
  });

  vueSfcAst.template.content = templateRootNode.toString();

  console.log('updated node:', selectedNode);
  console.log('updated vueSfcAst:', vueSfcAst);

  return vueSfcAst;
}

/**
 * 在<template>的目标节点前添加兄弟节点
 */
export function insertSiblingBeforeTargetNode(
  vueSfcAst: any,
  targetNodeId: string,
  newNode: any
) {
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  const targetNode: any = templateRootNode.querySelector(`[${SLOT.dnd}="${targetNodeId}"]`);

  const insertedNodeContent = `${newNode}\n`;
  targetNode.insertAdjacentHTML('beforebegin', insertedNodeContent);

  vueSfcAst.template.content = templateRootNode.toString();

  return vueSfcAst;
}

/**
 * 在<template>的目标节点后添加兄弟节点
 * @param vueSfcAst 
 * @param targetNodeId 
 * @param newNode 
 */
export function insertSiblingAfterTargetNode(
  vueSfcAst: any,
  targetNodeId: string,
  newNode: any
) {
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  const targetNode: any = templateRootNode.querySelector(`[${SLOT.dnd}="${targetNodeId}"]`);

  const insertedNodeContent = `\n${newNode}`;
  targetNode.insertAdjacentHTML('afterend', insertedNodeContent);

  vueSfcAst.template.content = templateRootNode.toString();

  return vueSfcAst;
}

/**
 * 在<template>的目标节点中添加子节点
 * @param vueSfcAst 
 * @param targetNodeId 
 * @param newNode 
 * @returns 
 */
export function appendChildToTargetNode(
  vueSfcAst: any,
  targetNodeId: string,
  newNode: any
) {
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  const targetNode: any = templateRootNode.querySelector(`[${SLOT.dnd}="${targetNodeId}"]`);

  const appendedNodeContent = `
    \n${newNode}\n
  `;
  targetNode.insertAdjacentHTML('beforeend', appendedNodeContent);

  vueSfcAst.template.content = templateRootNode.toString();

  return vueSfcAst;
}

/**
 * 在<template>中删除目标节点
 * @param vueSfcAst 
 * @param targetNodeId 
 */
export function removeTargetNode(
  vueSfcAst: any,
  targetNodeId: string,
) {
  const templateRootNode = nodeHtmlParser.parse(vueSfcAst?.template?.content);
  templateRootNode.querySelector(`[${SLOT.dnd}="${targetNodeId}"]`)?.remove();

  vueSfcAst.template.content = templateRootNode.toString();

  return vueSfcAst;
}
