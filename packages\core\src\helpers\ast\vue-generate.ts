
/**
 * 格式化配置
 */
const FORMAT_CONFIG = {
  tabWidth: 2,
  useTabs: false,
  printWidth: 100,
  semi: true,
  singleQuote: true,
  trailingComma: true,
  bracketSpacing: true,
  endOfLine: 'lf',
};

/**
 * 格式化 JavaScript/TypeScript 代码
 * @param code 代码字符串
 * @returns 格式化后的代码
 */
function formatScript(code: string): string {
  if (!code || code.trim() === '') {
    return '';
  }

  // 简单的JavaScript格式化
  try {
    // 移除多余的空白行
    const lines = code.split('\n').filter(line => line.trim() !== '');

    // 基本的缩进处理
    let indentLevel = 0;
    const formattedLines = lines.map(line => {
      const trimmedLine = line.trim();

      // 减少缩进的情况
      if (trimmedLine.startsWith('}') || trimmedLine.startsWith(']') || trimmedLine.startsWith(')')
          || trimmedLine.includes('} else') || trimmedLine.includes('} catch') || trimmedLine.includes('} finally')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }

      const formattedLine = '  '.repeat(indentLevel) + trimmedLine;

      // 增加缩进的情况
      if (trimmedLine.endsWith('{') || trimmedLine.endsWith('[') || trimmedLine.endsWith('(')
          || trimmedLine.includes('if (') || trimmedLine.includes('for (') || trimmedLine.includes('while (')
          || trimmedLine.includes('function') || trimmedLine.includes('=>')) {
        indentLevel++;
      }

      return formattedLine;
    });

    return formattedLines.join('\n');
  } catch (error) {
    // 如果格式化失败，返回原始代码
    return code;
  }
}

/**
 * 解析HTML标签信息
 * @param line HTML行
 * @returns 标签信息
 */
function parseHtmlLine(line: string): {
  isStartTag: boolean;
  isEndTag: boolean;
  isSelfClosing: boolean;
  isComment: boolean;
  hasContent: boolean;
  tagName: string;
} {
  const trimmed = line.trim();

  return {
    isStartTag: trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.startsWith('<!--'),
    isEndTag: trimmed.startsWith('</'),
    isSelfClosing: trimmed.includes('/>') || /^<(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)\b/i.test(trimmed),
    isComment: trimmed.startsWith('<!--'),
    hasContent: !trimmed.startsWith('<'),
    tagName: trimmed.match(/<\/?([a-zA-Z0-9-]+)/)?.[1] || '',
  };
}

/**
 * 格式化 HTML 模板代码
 * @param code HTML 代码字符串
 * @returns 格式化后的代码
 */
function formatTemplate(code: string): string {
  if (!code || code.trim() === '') {
    return '';
  }

  try {
    // 预处理：标准化空白和换行
    let formatted = code
      .replace(/\s+/g, ' ')  // 合并多个空白为单个空格
      .trim();

    // 在标签之间添加换行
    formatted = formatted.replace(/>\s*</g, '>\n<');

    // 处理标签内的文本内容
    formatted = formatted.replace(/>\s*([^<\s][^<]*?)\s*</g, (_, content) => {
      const trimmedContent = content.trim();
      // 如果内容很短或包含Vue语法，保持在同一行
      if (trimmedContent.length <= 30 || /\{\{.*\}\}/.test(trimmedContent)) {
        return `>${trimmedContent}<`;
      } else {
        // 长文本内容换行
        return `>\n  ${trimmedContent}\n<`;
      }
    });

    const lines = formatted.split('\n').filter(line => line.trim());
    const formattedLines: string[] = [];
    let indentLevel = 0;
    const tagStack: string[] = []; // 用于跟踪标签栈

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      if (!trimmedLine) continue;

      const tagInfo = parseHtmlLine(trimmedLine);

      // 处理结束标签
      if (tagInfo.isEndTag) {
        // 弹出标签栈
        if (tagStack.length > 0 && tagStack[tagStack.length - 1] === tagInfo.tagName) {
          tagStack.pop();
        }
        indentLevel = Math.max(0, indentLevel - 1);
        const indentedLine = '  '.repeat(indentLevel) + trimmedLine;
        formattedLines.push(indentedLine);
      }
      // 处理文本内容
      else if (tagInfo.hasContent) {
        const indentedLine = '  '.repeat(indentLevel) + trimmedLine;
        formattedLines.push(indentedLine);
      }
      // 处理开始标签
      else if (tagInfo.isStartTag) {
        const indentedLine = '  '.repeat(indentLevel) + trimmedLine;
        formattedLines.push(indentedLine);

        // 如果不是自闭合标签，增加缩进并推入标签栈
        if (!tagInfo.isSelfClosing) {
          tagStack.push(tagInfo.tagName);
          indentLevel++;
        }
      }
      // 处理其他内容（注释等）
      else {
        const indentedLine = '  '.repeat(indentLevel) + trimmedLine;
        formattedLines.push(indentedLine);
      }
    }

    return formattedLines.join('\n');
  } catch (error) {
    return code;
  }
}

/**
 * 格式化 CSS 样式代码
 * @param code CSS 代码字符串
 * @returns 格式化后的代码
 */
function formatStyle(code: string): string {
  if (!code || code.trim() === '') {
    return '';
  }

  try {
    // 简单的CSS格式化
    let formatted = code;

    // 移除多余的空白
    formatted = formatted.replace(/\s+/g, ' ').trim();

    // 在规则之间添加换行
    formatted = formatted.replace(/}\s*/g, '}\n');
    formatted = formatted.replace(/{\s*/g, ' {\n');
    formatted = formatted.replace(/;\s*/g, ';\n');

    // 基本的缩进处理
    const lines = formatted.split('\n');
    let indentLevel = 0;
    const formattedLines = lines.map(line => {
      const trimmedLine = line.trim();

      if (!trimmedLine) return '';

      if (trimmedLine === '}') {
        indentLevel = Math.max(0, indentLevel - 1);
        return '  '.repeat(indentLevel) + trimmedLine;
      }

      const formattedLine = '  '.repeat(indentLevel) + trimmedLine;

      if (trimmedLine.includes('{')) {
        indentLevel++;
      }

      return formattedLine;
    });

    return formattedLines.filter(line => line.trim()).join('\n');
  } catch (error) {
    return code;
  }
}

/**
 * 生成格式化的 script 标签
 * @param vueSfcAst Vue SFC AST
 * @returns 格式化的 script 标签字符串
 */
function generateScriptSection(vueSfcAst: any): string {
  const scriptSetup = vueSfcAst.scriptSetup;
  const script = vueSfcAst.script;

  if (scriptSetup) {
    const formattedContent = formatScript(scriptSetup.content || '');
    const attrs = scriptSetup.attrs || {};
    const langAttr = attrs.lang ? ` lang="${attrs.lang}"` : '';

    if (formattedContent) {
      return `<script setup${langAttr}>\n${formattedContent}\n</script>`;
    } else {
      return `<script setup${langAttr}></script>`;
    }
  } else if (script) {
    const formattedContent = formatScript(script.content || '');
    const attrs = script.attrs || {};
    const langAttr = attrs.lang ? ` lang="${attrs.lang}"` : '';

    if (formattedContent) {
      return `<script${langAttr}>\n${formattedContent}\n</script>`;
    } else {
      return `<script${langAttr}></script>`;
    }
  }

  return '';
}

/**
 * 生成格式化的 template 标签
 * @param vueSfcAst Vue SFC AST
 * @returns 格式化的 template 标签字符串
 */
function generateTemplateSection(vueSfcAst: any): string {
  const template = vueSfcAst.template;

  if (!template) {
    return '';
  }

  const formattedContent = formatTemplate(template.content || '');
  const attrs = template.attrs || {};
  const langAttr = attrs.lang ? ` lang="${attrs.lang}"` : '';

  if (formattedContent) {
    return `<template${langAttr}>\n${formattedContent}\n</template>`;
  } else {
    return `<template${langAttr}></template>`;
  }
}

/**
 * 生成格式化的 style 标签
 * @param vueSfcAst Vue SFC AST
 * @returns 格式化的 style 标签字符串数组
 */
function generateStyleSections(vueSfcAst: any): string[] {
  const styles = vueSfcAst.styles || [];

  return styles.map((style: any) => {
    const formattedContent = formatStyle(style.content || '');
    const attrs = style.attrs || {};

    // 构建属性字符串
    const attrStrings: string[] = [];
    if (attrs.lang) {
      attrStrings.push(`lang="${attrs.lang}"`);
    }
    if (attrs.scoped) {
      attrStrings.push('scoped');
    }
    if (attrs.module) {
      attrStrings.push('module');
    }

    const attrString = attrStrings.length > 0 ? ` ${attrStrings.join(' ')}` : '';

    if (formattedContent) {
      return `<style${attrString}>\n${formattedContent}\n</style>`;
    } else {
      return `<style${attrString}></style>`;
    }
  });
}

/**
 * 基于Vue SFC的AST重新生成格式化的.vue文件
 * @param vueSfcAst Vue SFC AST
 * @returns 格式化的Vue SFC代码
 */
export function ast2vue(vueSfcAst: any): string {
  if (!vueSfcAst) {
    return '';
  }

  const sections: string[] = [];

  // 生成 script 部分
  const scriptSection = generateScriptSection(vueSfcAst);
  if (scriptSection) {
    sections.push(scriptSection);
  }

  // 生成 template 部分
  const templateSection = generateTemplateSection(vueSfcAst);
  if (templateSection) {
    sections.push(templateSection);
  }

  // 生成 style 部分
  const styleSections = generateStyleSections(vueSfcAst);
  if (styleSections.length > 0) {
    sections.push(...styleSections);
  }

  // 用双换行连接各个部分
  return `${sections.join('\n\n')}\n`;
}
