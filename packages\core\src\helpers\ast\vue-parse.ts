/**
 * #Vue3 Supported  
 */
import { parse as parseVue3 } from '@vue/compiler-sfc';
// import { compile as parseVue3Template } from '@vue/compiler-dom';

/**
 * 将Vue SFC解析为AST
 * @param code 
 * @returns 
 */
export function vue2ast(code: string): any {
  const { descriptor } = parseVue3(code);
  // const { code: templateCode } = parseVue3Template(descriptor.template.content);
  console.log('Vue descriptor', descriptor);

  return descriptor;
}
