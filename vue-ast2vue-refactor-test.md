# Vue ast2vue 方法重构测试

## 重构概述

重构了`ast2vue`方法，实现了对Vue SFC代码的格式化生成，包括`<script>`、`<template>`和`<style>`部分的完整格式化支持。

## 重构内容

### 1. 代码格式化功能

#### 1.1 JavaScript/TypeScript 格式化 (`formatScript`)
- **基本缩进处理**：自动处理函数、条件语句、循环语句的缩进
- **代码清理**：移除多余的空白行
- **语法识别**：识别`{}`、`[]`、`()`等语法结构进行正确缩进

```typescript
// 输入
const data=ref([{name:'test',value:123}]);function handleClick(){console.log('clicked');}

// 输出
const data = ref([{
  name: 'test',
  value: 123
}]);
function handleClick() {
  console.log('clicked');
}
```

#### 1.2 HTML 模板格式化 (`formatTemplate`)
- **标签缩进**：自动处理HTML标签的嵌套缩进
- **换行处理**：在标签之间添加适当的换行
- **空白清理**：移除多余的空白字符

```html
<!-- 输入 -->
<div><h1>Title</h1><p>Content</p></div>

<!-- 输出 -->
<div>
  <h1>Title</h1>
  <p>Content</p>
</div>
```

#### 1.3 CSS 样式格式化 (`formatStyle`)
- **规则缩进**：CSS规则的正确缩进
- **属性换行**：每个CSS属性占一行
- **括号处理**：正确处理CSS选择器和规则的括号

```css
/* 输入 */
.container{padding:10px;margin:0;}.title{font-size:16px;color:#333;}

/* 输出 */
.container {
  padding: 10px;
  margin: 0;
}
.title {
  font-size: 16px;
  color: #333;
}
```

### 2. Vue SFC 部分生成

#### 2.1 Script 部分生成 (`generateScriptSection`)
- **支持 `<script setup>`**：优先使用setup语法
- **支持传统 `<script>`**：兼容传统Vue语法
- **属性处理**：正确处理`lang`等属性
- **内容格式化**：自动格式化JavaScript/TypeScript代码

```vue
<!-- 生成示例 -->
<script setup lang="ts">
import { ref, reactive } from 'vue'

const count = ref(0)
const state = reactive({
  name: 'Vue',
  version: '3.0'
})

function increment() {
  count.value++
}
</script>
```

#### 2.2 Template 部分生成 (`generateTemplateSection`)
- **HTML格式化**：自动格式化模板内容
- **属性支持**：支持`lang`等模板属性
- **缩进处理**：正确的HTML标签缩进

```vue
<!-- 生成示例 -->
<template>
  <div class="container">
    <h1>{{ title }}</h1>
    <button @click="increment">
      Count: {{ count }}
    </button>
  </div>
</template>
```

#### 2.3 Style 部分生成 (`generateStyleSections`)
- **多样式支持**：支持多个`<style>`标签
- **属性处理**：正确处理`scoped`、`module`、`lang`等属性
- **CSS格式化**：自动格式化CSS代码

```vue
<!-- 生成示例 -->
<style scoped>
.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-size: 24px;
  color: #2c3e50;
  margin-bottom: 16px;
}
</style>

<style lang="scss">
$primary-color: #42b883;

.button {
  background-color: $primary-color;
  
  &:hover {
    opacity: 0.8;
  }
}
</style>
```

### 3. 主要的 ast2vue 函数

#### 3.1 完整的 SFC 生成
```typescript
export function ast2vue(vueSfcAst: any): string {
  if (!vueSfcAst) {
    return '';
  }

  const sections: string[] = [];

  // 生成 script 部分
  const scriptSection = generateScriptSection(vueSfcAst);
  if (scriptSection) {
    sections.push(scriptSection);
  }

  // 生成 template 部分
  const templateSection = generateTemplateSection(vueSfcAst);
  if (templateSection) {
    sections.push(templateSection);
  }

  // 生成 style 部分
  const styleSections = generateStyleSections(vueSfcAst);
  if (styleSections.length > 0) {
    sections.push(...styleSections);
  }

  // 用双换行连接各个部分
  return `${sections.join('\n\n')}\n`;
}
```

#### 3.2 输出格式
生成的Vue SFC文件具有以下特点：
- **部分分离**：script、template、style部分用双换行分隔
- **正确缩进**：每个部分内容都有正确的缩进
- **属性保留**：保留原有的标签属性（lang、scoped等）
- **格式统一**：统一的代码格式化风格

## 测试用例

### 测试用例 1：基本 Vue SFC
```javascript
// 输入 AST
const vueSfcAst = {
  scriptSetup: {
    content: "import{ref}from'vue';const count=ref(0);",
    attrs: { lang: 'ts' }
  },
  template: {
    content: "<div><h1>{{title}}</h1><button @click='increment'>{{count}}</button></div>",
    attrs: {}
  },
  styles: [{
    content: ".container{padding:10px;}.button{color:blue;}",
    attrs: { scoped: true }
  }]
};

// 期望输出
const expected = `<script setup lang="ts">
import { ref } from 'vue';
const count = ref(0);
</script>

<template>
  <div>
    <h1>{{ title }}</h1>
    <button @click="increment">{{ count }}</button>
  </div>
</template>

<style scoped>
.container {
  padding: 10px;
}
.button {
  color: blue;
}
</style>
`;
```

### 测试用例 2：复杂嵌套结构
```javascript
// 输入 AST
const complexAst = {
  scriptSetup: {
    content: "import{reactive,computed}from'vue';const state=reactive({users:[],loading:false});const filteredUsers=computed(()=>state.users.filter(u=>u.active));",
    attrs: { lang: 'ts' }
  },
  template: {
    content: "<main><div class='header'><h1>Users</h1></div><div class='content'><div v-for='user in filteredUsers' :key='user.id' class='user-card'><span>{{user.name}}</span></div></div></main>",
    attrs: {}
  },
  styles: [{
    content: ".header{background:#f5f5f5;padding:1rem;}.content{padding:1rem;}.user-card{border:1px solid #ddd;margin:0.5rem 0;padding:1rem;}",
    attrs: { scoped: true }
  }]
};
```

## 技术特点

### 1. 渐进式格式化
- **容错性强**：格式化失败时返回原始代码
- **逐步改进**：可以逐步增强格式化规则
- **性能优化**：避免复杂的AST操作，使用字符串处理

### 2. Vue 生态兼容
- **Vue 3 支持**：完全支持Vue 3的Composition API
- **TypeScript 支持**：支持TypeScript语法
- **预处理器支持**：支持Sass、Less等CSS预处理器

### 3. 可扩展性
- **模块化设计**：每个格式化功能独立
- **配置化**：通过FORMAT_CONFIG统一配置格式化选项
- **插件化**：可以轻松添加新的格式化规则

## 使用效果

### 重构前
```vue
<script setup>import { ref } from 'vue'
const count = ref(0)</script><template><div><h1>Count: {{count}}</h1></div></template><style>.container{padding:10px}</style>
```

### 重构后
```vue
<script setup>
import { ref } from 'vue'

const count = ref(0)
</script>

<template>
  <div>
    <h1>Count: {{ count }}</h1>
  </div>
</template>

<style>
.container {
  padding: 10px;
}
</style>
```

## 总结

重构后的`ast2vue`方法提供了：

- ✅ **完整的代码格式化**：支持JavaScript、HTML、CSS的格式化
- ✅ **Vue SFC 结构化输出**：正确的部分分离和缩进
- ✅ **属性保留**：保留所有标签属性
- ✅ **容错处理**：格式化失败时的优雅降级
- ✅ **性能优化**：高效的字符串处理
- ✅ **可维护性**：模块化的代码结构

这使得生成的Vue文件具有良好的可读性和一致的代码风格，大大提升了开发体验。
