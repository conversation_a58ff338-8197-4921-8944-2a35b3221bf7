import { JSXElement } from '@babel/types';
import {
  IComponentPrototype,
  Dict,
  ITangoConfigJson,
  hasFileExtension,
  isString,
  logger,
  uniq,
  setValue,
} from '@music163/tango-helpers';
import {
  prototype2node,
  getFilepath,
  isPathnameMatchRoute,
  getJSXElementChildrenNames,
  namesToImportDeclarations,
  prototype2importDeclarationData,
} from '../helpers';
import { DropMethod } from './drop-target';
import { HistoryMessage, TangoHistory } from './history';
import { JsxViewNode } from './view-node';
import { IFileConfig, FileType, ITangoConfigPackages, IPageConfigData, IFileError } from '../types';
import { SelectSource } from './select-source';
import { DragSource } from './drag-source';
import { JsRouteConfigFile } from './js-route-config-file';
import { JsViewFile } from './js-view-file';
import { VueViewFile } from './vue-view-file';
import { JsLocalComponentsEntryFile } from './js-local-components-entry-file';
import { JsAppEntryFile } from './js-app-entry-file';
import { AbstractFile } from './abstract-file';
import { JsonFile } from './json-file';
import { AbstractJsFile } from './abstract-js-file';
import { VueRouteConfigFile } from './vue-route-config-file';

export interface IWorkspaceInitConfig {
  /**
   * 项目类型
   */
  projectType?: 'react' | 'vue3';
  /**
   * 入口文件
   */
  entry?: string;
  /**
   * 初始化文件列表
   */
  files?: IFileConfig[];
  /**
   * 默认的激活的路由
   */
  defaultActiveRoute?: string;
  /**
   * 组件描述列表
   */
  prototypes?: Record<string, IComponentPrototype>;
  /**
   * 工作区文件变更事件
   */
  onFilesChange?: AbstractWorkspace['onFilesChange'];
}

/**
 * Workspace 抽象基类
 */
export abstract class AbstractWorkspace extends EventTarget {
  /**
   * 项目类型
   */
  projectType: 'react' | 'vue3';
  /**
   * 历史记录
   */
  history: TangoHistory;
  /**
   * 选中源
   */
  selectSource: SelectSource;
  /**
   * 拖拽源
   */
  dragSource: DragSource;

  /**
   * 工作区的文件列表
   */
  files: Map<string, AbstractFile>;

  /**
   * 组件配置
   */
  componentPrototypes: Map<string, IComponentPrototype>;

  /**
   * 入口文件
   */
  entry: string;

  /**
   * 当前路由
   */
  activeRoute: string;

  /**
   * 当前选中的文件
   */
  activeFile: string;

  /**
   * 当前选中的视图文件
   */
  activeViewFile: string;

  /**
   * 应用入口模块
   */
  jsAppEntryFile: JsAppEntryFile;

  /**
   * 路由配置模块
   */
  routeModule: JsRouteConfigFile | VueRouteConfigFile;

  /**
   * 组件入口模块
   */
  componentsEntryModule: JsLocalComponentsEntryFile;

  /**
   * package.json 文件
   */
  packageJson: JsonFile;

  /**
   * tango.config.json 文件
   */
  tangoConfigJson: JsonFile;

  /**
   * 绑定事件
   */
  on = this.addEventListener;

  /**
   * 移除事件
   */
  off = this.removeEventListener;

  /**
   * 工作区是否就绪
   */
  private isReady: boolean;

  /**
   * 拷贝的暂存区
   */
  private copyTempNodes: JsxViewNode[];

  get isValid() {
    return !!this.tangoConfigJson && !!this.activeViewModule && this.fileErrors.length === 0;
  }

  /**
   * 项目配置，返回解析后的 tango.config.json 文件
   */
  get projectConfig() {
    return this.tangoConfigJson?.json as ITangoConfigJson;
  }

  /**
   * 当前激活的视图模块
   */
  get activeViewModule() {
    if (!this.activeViewFile) {
      this.setActiveViewFile(this.activeRoute);
    }
    return this.files.get(this.activeViewFile) as JsViewFile;
  }

  /**
   * 获取页面列表
   */
  get pages() {
    const ret: IPageConfigData[] = [];
    this.routeModule?.routes.forEach((item) => {
      if (item.path !== '*') {
        ret.push({
          path: item.path,
          name: item.component,
        });
      }
    });
    return ret;
  }

  get bizComps(): string[] {
    const packages = this.tangoConfigJson?.getValue('packages');
    let list = this.tangoConfigJson?.getValue('bizDependencies') || [];
    if (packages) {
      list = [
        ...new Set([
          ...list,
          ...Object.keys(packages).filter((e) => packages[e].type === 'bizDependency'),
        ]),
      ];
    }
    return list;
  }

  get baseComps(): string[] {
    const packages = this.tangoConfigJson?.getValue('packages');
    let list = this.tangoConfigJson?.getValue('baseDependencies') || [];
    if (packages) {
      list = [
        ...new Set([
          ...list,
          ...Object.keys(packages).filter((e) => packages[e].type === 'baseDependency'),
        ]),
      ];
    }
    return list;
  }

  get localComps(): string[] {
    return Object.keys(this.componentsEntryModule?.exportList || {});
  }

  get fileErrors() {
    const errors: IFileError[] = [];
    this.files.forEach((file) => {
      if (file.isError) {
        errors.push({
          filename: file.filename,
          message: file.errorMessage,
        });
      }
    });
    return errors;
  }

  constructor(options?: IWorkspaceInitConfig) {
    super();
    this.projectType = options?.projectType || 'react';
    this.history = new TangoHistory(this);
    this.selectSource = new SelectSource(this);
    this.dragSource = new DragSource(this);
    this.componentPrototypes = new Map();
    this.entry = options?.entry;
    this.activeRoute = options?.defaultActiveRoute || '/';
    this.activeFile = options?.entry;
    this.activeViewFile = '';
    this.files = new Map();
    this.isReady = false;

    if (options?.onFilesChange) {
      // 使用用户提供的 onFilesChange
      this.onFilesChange = options.onFilesChange;
    }

    if (options?.prototypes) {
      this.setComponentPrototypes(options.prototypes);
    }
  }

  getPrototype(name: string | IComponentPrototype) {
    if (isString(name)) {
      return this.componentPrototypes.get(name);
    }
    return name as IComponentPrototype;
  }

  /**
   * 设置当前路由
   * @param routePath 路由路径
   */
  setActiveRoute(routePath: string) {
    if (routePath === this.activeRoute) {
      return;
    }
    this.selectSource.clear();
    this.activeRoute = routePath;
    this.setActiveViewFile(routePath);
  }

  /**
   * 设置当前选中的文件
   * @param filename
   */
  setActiveFile(filename: string, isViewFile = false) {
    this.activeFile = filename;
    if (isViewFile) {
      this.activeViewFile = filename;
    }
  }

  /**
   * 根据当前的路由计算当前的视图模块
   */
  setActiveViewFile(routePath: string) {
    let filename = this.getFilenameByRoutePath(routePath);
    if (!filename) {
      // 没有找到 route 对应的文件，使用默认的 entry
      for (const [, file] of this.files) {
        if (file.type === FileType.JsViewFile) {
          filename = file.filename;
          break;
        }
      }
    }
    if (filename) {
      this.setActiveFile(filename, true);
    }
  }

  setComponentPrototypes(prototypes: Record<string, IComponentPrototype>) {
    Object.keys(prototypes).forEach((name) => {
      this.componentPrototypes.set(name, prototypes[name]);
    });
  }

  /**
   * 添加一组文件到工作区，如果文件同名，后面的文件会覆盖前面的文件
   * @param files
   */
  addFiles(files: IFileConfig[] = []) {
    files.forEach((file) => {
      this.addFile(file.filename, file.code, file.type);
    });
  }

  /**
   * 添加视图文件
   * @param viewName 文件名
   * @param code 代码
   */
  addViewFile(viewName: string, code: string) {
    const viewRoute = viewName.startsWith('/') ? viewName : `/${viewName}`;
    // #Vue3 supported
    const filename = 
      this.projectType === 'vue3' ? 
        `/src/views/${viewName}.vue` : 
        `/src/pages/${viewName}.js`;
    this.addFile(filename, code);
    this.addRoute(
      {
        name: viewName,
        path: viewRoute,
      },
      filename,
    );
  }

  updateFile(filename: string, code: string, isSyncAst = true) {
    const file = this.getFile(filename);
    if (file instanceof AbstractJsFile) {
      file.update(code, isSyncAst);
    } else {
      file.update(code);
    }

    const shouldFormat = this.projectConfig?.designerConfig?.autoFormatCode;
    if (shouldFormat && file instanceof JsViewFile) {
      file.removeUnusedImportSpecifiers().update();
    }
    this.history.push({
      message: HistoryMessage.UpdateCode,
      data: {
        [filename]: code,
      },
    });
  }

  syncFiles() {
    this.files.forEach((file) => {
      if (file instanceof AbstractJsFile) {
        file.updateAst();
      }
    });
  }

  removeFile(filename: string) {
    if (this.files.get(filename)) {
      // 如果是文件，直接删除
      this.files.delete(filename);
    } else {
      // 没有匹配到，就是一个目录，直接删除整个目录
      // FIXME: 可能存在风险，如果文件夹中的模块被复用，则会导致误删除
      Array.from(this.files.keys()).forEach((key) => {
        if (key.startsWith(`${filename}/`)) {
          this.files.delete(key);
        }
      });
    }
  }

  /**
   * 重命名文件
   * @param oldFilename
   * @param newFilename
   */
  renameFile(oldFilename: string, newFilename: string) {
    const file = this.files.get(oldFilename);
    if (file) {
      this.removeFile(oldFilename);
      this.addFile(newFilename, file.code);
    }
  }

  /**
   * 重命名文件夹
   * @param oldFoldername 旧文件夹名
   * @param newFoldername 新文件夹名
   */
  renameFolder(oldFoldername: string, newFoldername: string) {
    Array.from(this.files.keys()).forEach((key) => {
      if (key.startsWith(`${oldFoldername}/`)) {
        const newKey = key.replace(oldFoldername, newFoldername);
        this.renameFile(key, newKey);
      }
    });
  }

  /**
   * 根据文件名获取文件对象
   * @param filename
   * @returns
   */
  getFile(filename: string) {
    return this.files.get(filename);
  }

  /**
   * 获取文件列表
   * @returns { [filename]: fileCode }
   */
  listFiles() {
    const ret: Dict<string> = {};
    this.files.forEach((file) => {
      ret[file.filename] = file.cleanCode;
    });
    return ret;
  }

  /**
   * 删除视图模块
   * @param route 路由名称
   */
  removeViewModule(routePath: string) {
    // get filename first
    const filename = this.getFilenameByRoutePath(routePath);
    if (this.routeModule) {
      this.routeModule.removeRoute(routePath).update();
      this.setActiveRoute(this.routeModule.routes[0]?.path || '/');
    }
    this.removeFile(filename);
  }

  /**
   * 添加新的路由
   */
  addRoute(routeData: IPageConfigData, importFilePath: string) {
    this.routeModule?.addRoute(routeData.path, importFilePath).update();
  }

  /**
   * 更新页面路由配置
   * @param sourceRoutePath
   * @param targetPageData
   */
  updateRoute(sourceRoutePath: string, targetPageData: IPageConfigData) {
    if (sourceRoutePath !== targetPageData.path) {
      this.routeModule?.updateRoute(sourceRoutePath, targetPageData.path).update();
    }
  }

  /**
   * 复制视图文件
   * @param sourceRoute
   * @param targetRouteConfig
   */
  copyViewPage(sourceRoutePath: string, targetPageData: IPageConfigData) {
    const sourceFilePath = this.getRealViewFilePath(this.getFilenameByRoutePath(sourceRoutePath));
    const targetFilePath = getFilepath(targetPageData.path, '/src/pages');
    this.copyFiles(sourceFilePath, targetFilePath);
    this.addRoute(targetPageData, targetFilePath);
  }

  getNode(id: string, filename?: string) {
    const file = filename ? this.getFile(filename) : this.activeViewModule;
    if (file instanceof JsViewFile || file instanceof VueViewFile) {
      return file.getNode(id);
    }
  }

  /**
   * 应用代码初始化完成
   */
  ready() {
    if (!this.isReady) {
      this.isReady = true;

      this.history.push({
        message: HistoryMessage.InitView,
        data: {
          [this.activeViewModule?.filename]: this.activeViewModule?.code,
        },
      });
    }
  }

  addDependency(data: any) {
    // TODO: implement it to replace addBizComp & addServiceComp
  }

  /**
   * 获取 package.json 中的依赖列表
   * @returns
   * TODO: fix this logic to merge dependencies from package.json and tango.config.json
   */
  listDependencies() {
    return this.packageJson?.getValue('dependencies');
  }

  getDependency(pkgName: string) {
    const packages = this.tangoConfigJson?.getValue('packages');
    const dependencies = this.packageJson?.getValue('dependencies'); // 兼容老版本
    const detail = {
      version: dependencies?.[pkgName],
      ...(packages?.[pkgName] || {}),
    };
    return detail;
  }

  /**
   * 更新依赖，没有就添加
   * @param name
   * @param version
   * FIXME: 参数3的设置需要重新考虑下
   */
  updateDependency(
    name: string,
    version: string,
    options?: {
      package?: ITangoConfigPackages;
      [x: string]: any;
    },
  ) {
    this.packageJson
      ?.setValue('dependencies', (deps = {}) => {
        deps[name] = version;
        return deps;
      })
      .update();

    this.tangoConfigJson
      ?.setValue('packages', (packages) => {
        // 兼容以前的逻辑，只在拥有 package 参数时，才会更新 packages 字段
        if (!packages) {
          return undefined;
        }

        setValue(packages, name, {
          ...packages[name], // 保留原有的配置
          version, // 更新版本号
          ...options?.package, // 更新 package 配置
        });

        return packages;
      })
      .update();

    this.history.push({
      message: HistoryMessage.UpdateDependency,
      data: {
        [this.packageJson.filename]: this.packageJson.code,
      },
    });
  }

  /**
   * 移除依赖
   * @param name
   */
  removeDependency(name: string) {
    this.packageJson
      ?.setValue('dependencies', (deps) => {
        if (deps[name]) {
          delete deps[name];
        }
        return deps;
      })
      .update();

    this.tangoConfigJson
      ?.setValue('packages', (packages = {}) => {
        if (packages?.[name]) {
          delete packages[name];
        }

        return packages;
      })
      .update();

    this.history.push({
      message: HistoryMessage.RemoveDependency,
      data: {
        [this.packageJson.filename]: this.packageJson.code,
      },
    });
  }

  /**
   * 删除业务组件
   * @param name
   */
  removeBizComp(name: string) {
    this.tangoConfigJson
      ?.setValue('bizDependencies', (deps?: string[]) => {
        if (!deps) {
          return undefined;
        }
        return deps.filter((dep) => dep !== name);
      })
      .update();
    this.removeDependency(name);
  }

  /**
   * 添加业务组件
   * @param name
   */
  addBizComp(
    name: string,
    version: string,
    options?: {
      package?: ITangoConfigPackages;
      [x: string]: any;
    },
  ) {
    const packages = this.tangoConfigJson.getValue('packages');
    this.updateDependency(name, version, {
      ...options,
      ...(!!packages && {
        package: {
          ...options?.package,
          type: 'bizDependency',
        },
      }),
    });

    // 兼容以前的逻辑
    if (!options?.package && !packages) {
      // TODO: if tangoConfigJson not found, init this file
      this.tangoConfigJson
        ?.setValue('bizDependencies', (deps: string[] = []) => {
          if (!deps.includes(name)) {
            deps.push(name);
          }
          return deps;
        })
        .update();
    }

    this.tangoConfigJson &&
      this.history.push({
        message: HistoryMessage.UpdateDependency,
        data: {
          [this.tangoConfigJson.filename]: this.tangoConfigJson.code,
        },
      });
  }

  /**
   * 删除选中节点
   */
  removeSelectedNode() {
    const file = this.selectSource.file;
    if (!file) return;

    // 选中的结点一定位于相同的文件中
    this.selectSource.nodes.forEach((node) => {
      file.removeNode(node.id);
    });
    file.update();
    this.selectSource.clear();
    this.history.push({
      message: HistoryMessage.RemoveNode,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  /**
   * 复制选中结点
   */
  copySelectedNode() {
    this.copyTempNodes = this.selectSource.nodes as JsxViewNode[];
  }

  /**
   * 粘贴选中结点
   * @deprecated 考虑废弃
   * TODO: 重构该逻辑，抽离出公共的方法
   */
  pasteSelectedNode() {
    if (this.selectSource.size !== 1) return;
    if (!this.copyTempNodes || this.copyTempNodes.length === 0) return;

    // 过滤掉无效的节点（rawNode为null或undefined的节点）
    const validCopyNodes = this.copyTempNodes.filter((node) => node && node.rawNode);

    if (validCopyNodes.length === 0) {
      logger.warn('No valid nodes to paste');
      return;
    }

    // TODO: 潜在隐患，如果跨页的话，代码里的逻辑调用也要处理

    const importDeclarations = this.getImportDeclarationByNodes(
      validCopyNodes.map((node) => node.rawNode),
    );

    importDeclarations.forEach((importDeclaration) => {
      this.activeViewModule.updateImportSpecifiersLegacy(importDeclaration);
    });

    validCopyNodes.forEach((node) => {
      this.activeViewModule.insertAfter(this.selectSource.first.id, node.cloneRawNode());
    });

    this.activeViewModule.update();

    this.history.push({
      message: HistoryMessage.CloneNode,
      data: {
        [this.activeViewModule.filename]: this.activeViewModule.code,
      },
    });
  }

  /**
   * 克隆选中节点，追加到当前结点的后方
   */
  cloneSelectedNode() {
    const file = this.selectSource.file;

    let injectedProps;
    if (this.projectConfig?.designerConfig?.autoGenerateComponentId) {
      // 生成新的组件 id
      injectedProps = {
        tid: file.idGenerator.generateId(this.selectSource.firstNode.component).id,
      };
    }

    file
      .insertAfter(
        this.selectSource.first.id,
        this.selectSource.firstNode.cloneRawNode(injectedProps) as JSXElement,
      )
      .update();

    this.history.push({
      message: HistoryMessage.CloneNode,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  /**
   * 在目标节点中插入子节点
   * @param targetNodeId 目标节点dnd-id
   * @param sourceName 插入的组件名称
   * @returns
   */
  insertToNode(targetNodeId: string, sourceName: string | IComponentPrototype) {
    if (!targetNodeId || !sourceName) {
      return;
    }

    const sourcePrototype = this.getPrototype(sourceName);
    const newNode = prototype2node(this.projectType, sourcePrototype);
    const file = this.getNode(targetNodeId).file;
    const { source, specifiers } = prototype2importDeclarationData(sourcePrototype, file.filename);
    file
      .insertChild(targetNodeId, newNode, 'last')
      .addImportSpecifiers(source, specifiers)
      .update();
    this.history.push({
      message: HistoryMessage.InsertNode,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  /**
   * 替换目标节点
   */
  replaceNode(targetNodeId: string, sourceName: string | IComponentPrototype) {
    if (!targetNodeId || !sourceName) {
      return;
    }

    const sourcePrototype = this.getPrototype(sourceName);
    const newNode = prototype2node(this.projectType, sourcePrototype);
    const file = this.getNode(targetNodeId).file;
    const { source, specifiers } = prototype2importDeclarationData(sourcePrototype, file.filename);
    file.replaceNode(targetNodeId, newNode).addImportSpecifiers(source, specifiers).update();
    this.history.push({
      message: HistoryMessage.ReplaceNode,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  /**
   * 在选中节点中插入子节点
   * @param childName 节点名
   */
  insertToSelectedNode(childName: string | IComponentPrototype) {
    const insertedPrototype = this.getPrototype(childName);
    if (insertedPrototype) {
      const newNode = prototype2node(this.projectType, insertedPrototype);
      const file = this.selectSource.file;
      const { source, specifiers } = prototype2importDeclarationData(
        insertedPrototype,
        file.filename,
      );
      file
        .insertChild(this.selectSource.first.id, newNode, 'last')
        .addImportSpecifiers(source, specifiers)
        .update();
      this.history.push({
        message: HistoryMessage.InsertNode,
        data: {
          [file.filename]: file.code,
        },
      });
    }
  }

  insertBeforeSelectedNode(sourceName: string | IComponentPrototype) {
    if (!sourceName) {
      return;
    }
    const targetNodeId = this.selectSource.first.id;
    const sourcePrototype = this.getPrototype(sourceName);
    const newNode = prototype2node(this.projectType, sourcePrototype);
    const file = this.getNode(targetNodeId).file;
    const { source, specifiers } = prototype2importDeclarationData(sourcePrototype, file.filename);

    file.insertBefore(targetNodeId, newNode).addImportSpecifiers(source, specifiers).update();

    this.history.push({
      message: HistoryMessage.InsertBeforeNode,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  insertAfterSelectedNode(sourceName: string | IComponentPrototype) {
    if (!sourceName) {
      return;
    }
    const targetNodeId = this.selectSource.first.id;
    const sourcePrototype = this.getPrototype(sourceName);
    const newNode = prototype2node(this.projectType, sourcePrototype);
    const file = this.getNode(targetNodeId).file;
    const { source, specifiers } = prototype2importDeclarationData(sourcePrototype, file.filename);

    file.insertAfter(targetNodeId, newNode).addImportSpecifiers(source, specifiers).update();

    this.history.push({
      message: HistoryMessage.InsertAfterNode,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  updateSelectedNodeAttributes(
    attributes: Record<string, any> = {},
    relatedImports: string[] = [],
  ) {
    const file = this.selectSource.file;
    console.log('selectSource', this.selectSource);
    file.updateNodeAttributes(this.selectSource.first.id, attributes, relatedImports).update();
    this.history.push({
      message: HistoryMessage.UpdateAttribute,
      data: {
        [file.filename]: file.code,
      },
    });
  }

  /**
   * 将节点拽入视图中
   */
  dropNode() {
    const dragSource = this.dragSource;
    const dropTarget = dragSource.dropTarget;

    if (!dragSource.prototype || !dropTarget.id) {
      // 无效的 drag source 或 drop target，提前退出
      logger.error('invalid dragSource or dropTarget');
      return;
    }

    // TODO: 这里需要一个额外的信息，DropTarget 的最近容器节点，用于判断目标元素是否可以被置入容器中
    const dragSourcePrototype = dragSource.prototype;

    let injectedProps; // 额外注入给节点的属性
    if (this.projectConfig?.designerConfig?.autoGenerateComponentId) {
      injectedProps = {
        tid: this.activeViewModule.idGenerator.generateId(dragSource.prototype.name).id,
      };
    }

    let newNode;
    if (dragSource.id) {
      // 来自画布，完整的克隆该节点
      newNode = dragSource.getNode().cloneRawNode();
    } else {
      // 来自物料面板，创建新的初始化节点
      newNode = prototype2node(this.projectType, dragSource.prototype, injectedProps);
    }

    if (!newNode) {
      return;
    }

    const targetFile = dropTarget.node?.file;
    const sourceFile = dragSource.node?.file;

    // dragSourcePrototype to importDeclarations
    const { source, specifiers } = prototype2importDeclarationData(
      dragSourcePrototype,
      targetFile.filename,
    );

    let isValidOperation = true;
    switch (dropTarget.method) {
      // 直接往目标节点的 children 里添加一个节点
      case DropMethod.InsertChild: {
        targetFile
          .insertChild(dropTarget.id, newNode, 'last')
          .addImportSpecifiers(source, specifiers);
        break;
      }
      case DropMethod.InsertFirstChild: {
        targetFile
          .insertChild(dropTarget.id, newNode, 'first')
          .addImportSpecifiers(source, specifiers);
        break;
      }
      // 往目标节点的后边插入一个节点
      case DropMethod.InsertAfter: {
        targetFile.insertAfter(dropTarget.id, newNode).addImportSpecifiers(source, specifiers);
        break;
      }
      // 往目标节点的前方插入一个节点
      case DropMethod.InsertBefore: {
        targetFile.insertBefore(dropTarget.id, newNode).addImportSpecifiers(source, specifiers);
        break;
      }
      // 替换目标节点
      case DropMethod.ReplaceNode: {
        targetFile.replaceNode(dropTarget.id, newNode).addImportSpecifiers(source, specifiers);
        break;
      }
      default:
        isValidOperation = false;
        break;
    }

    // 如果拖拽来源有 ID，表示来自画布
    const isDraggingFromView = !!dragSource.id;

    if (isValidOperation) {
      if (isDraggingFromView) {
        sourceFile.removeNode(dragSource.id);
      }

      this.selectSource.clear();
    }

    targetFile.update();
    if (isDraggingFromView && sourceFile.filename !== targetFile.filename) {
      sourceFile.update();
    }

    dragSource.clear();

    if (isValidOperation) {
      this.history.push({
        message: HistoryMessage.DropNode,
        data: {
          [targetFile.filename]: targetFile.code,
        },
      });
    }
  }

  onFilesChange(filenams: string[]) {
    // do nothing
  }

  /**
   * 刷新目标文件
   * @param filenames
   */
  refresh(filenames: string[]) {
    this.dispatchEvent(
      new CustomEvent('refresh', {
        detail: {
          filenames,
          entry: this.entry,
        },
      }),
    );
  }

  /**
   * 基于输入结点获得结点依赖的导入声明信息
   * @param nodes
   */
  private getImportDeclarationByNodes(nodes: JSXElement[]) {
    // 过滤掉无效的节点（null、undefined等），并进行严格的类型检查
    const validNodes = nodes.filter((node) => {
      if (!node) return false;

      // 检查是否是有效的对象且具有必要的属性
      if (typeof node !== 'object') return false;

      // 检查是否具有 JSXElement 的基本结构
      if (!node.type || node.type !== 'JSXElement') return false;
      if (!node.openingElement || !node.openingElement.name) return false;

      return true;
    });

    let names = validNodes.reduce((prev, cur) => {
      try {
        prev = prev.concat(getJSXElementChildrenNames(cur));
      } catch (error) {
        logger.error('Error processing node in getImportDeclarationByNodes:', error);
      }
      return prev;
    }, []);
    names = uniq(names);
    const importDeclarations = namesToImportDeclarations(names, this.selectSource.file.importMap);
    return importDeclarations;
  }

  /**
   * 根据路由路径获取文件名
   * @param routePath
   * @returns
   */
  private getFilenameByRoutePath(routePath: string) {
    let filename: string;
    // #Vue3 Supported
    this.routeModule?.routes.forEach((route) => {
      if (isPathnameMatchRoute(routePath, route.path) && route.importPath) {
        if (route.importPath.startsWith('@/')) {
          filename = route.importPath;
          const alias = this.tangoConfigJson.getValue('sandbox.alias') || {};
          if (alias['@']) {
            filename = filename.replace('@', alias['@']);
          }
          filename = this.getRealViewFilePath(filename);
        } else if (this.projectType === 'react' && route.importPath.startsWith('.')) {
          const absolutePath = route.importPath.replace('.', '/src');
          filename = this.getRealViewFilePath(absolutePath);
        } else if (this.projectType === 'vue3' && route.importPath.startsWith('..')) {
          const absolutePath = route.importPath.replace('..', '/src');
          filename = this.getRealViewFilePath(absolutePath);
        }
      }
    });

    return filename;
  }

  private getRealViewFilePath(filePath: string): string {
    // 如果有后缀名直接返回
    if (hasFileExtension(filePath)) {
      return filePath;
    }

    const possiblePaths = [
      `${filePath}.js`,
      `${filePath}.jsx`,
      `${filePath}/index.js`,
      `${filePath}/index.jsx`,
    ];

    for (const filepath of possiblePaths) {
      if (this.files.has(filepath)) {
        return filepath;
      }
    }
  }

  /**
   * 文件拷贝
   * @param sourcePath
   * @param targetPath
   */
  private copyFiles(sourceFilePath: string, targetFilePath: string) {
    if (this.files.has(sourceFilePath)) {
      // 来源是文件
      const file = this.files.get(sourceFilePath);
      this.addFile(`${targetFilePath}.js`, file.cleanCode, file.type);
    } else if (this.files.has(`${sourceFilePath}/index.js`)) {
      // 来源是目录
      Array.from(this.files.keys()).forEach((key) => {
        if (key.startsWith(`${sourceFilePath}/`)) {
          const sourceFile = this.getFile(key);
          this.addFile(
            targetFilePath + key.slice(sourceFilePath.length),
            sourceFile.cleanCode,
            sourceFile.type,
          );
        }
      });
    } else {
      logger.error('copyFiles failed, source: %s, target: %s', sourceFilePath, targetFilePath);
    }
  }

  abstract addFile(filename: string, code: string, fileType?: FileType): void;
}
