{"extends": ["eslint-config-ali/typescript/react", "prettier"], "ignorePatterns": ["**/dist/**/*", "**/lib/**/*", "**/node_modules/**/*", "scripts/**/*"], "rules": {"@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/dot-notation": "off", "@typescript-eslint/no-unused-vars": "warn", "import/no-cycle": "off", "no-nested-ternary": "off", "no-useless-return": "off", "no-param-reassign": "off", "prefer-destructuring": "off"}}