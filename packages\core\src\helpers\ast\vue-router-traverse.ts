import * as t from '@babel/types';
import traverse from '@babel/traverse';
import {
  Dict,
  upperCamelCase,
} from '@music163/tango-helpers';
import { 
  getImportDeclarationData,
  getLastImportDeclarationIndex
} from './traverse';
import { node2value } from './generate';
import type {
  IRouteData,
} from '../../types';

export function traverseVueRouteFile(ast: t.File) {
  const routes: IRouteData[] = [];
  const importMap: Dict<string> = {};
  console.log('[yushujie]route AST:', ast);

  traverse(ast, {
    ImportDeclaration(path) {
      const { defaultSpecifier, sourcePath } = getImportDeclarationData(path.node);
      if (defaultSpecifier) {
        importMap[defaultSpecifier] = sourcePath;
      }
    },
    ObjectExpression(path) {
      const { node } = path;
      const route = node2value(node, false);

      if (route.path && route.component) {
        // 同时具有path和component属性，才是路由配置项
        routes.push(route);
      }
    },
  });

  routes.forEach((item) => {
    if (item.component && importMap[item.component]) {
      item.importPath = importMap[item.component];
    }
  });

  return routes;
}

/**
 * 在路由配置文件中添加新的路由规则
 */
export function addRouteToVueRouteFile(ast: t.File, routePath: string, importFilePath: string) {
  if (/.jsx?$/.test(importFilePath)) {
    importFilePath = importFilePath.split('.')[0];
  }
  const component = upperCamelCase(routePath.split('/').join('-'));
  traverse(ast, {
    Program(path) {
      const lastImportIndex = getLastImportDeclarationIndex(path.node);
      path.node.body.splice(
        lastImportIndex,
        0,
        t.importDeclaration(
          [t.importDefaultSpecifier(t.identifier(component))],
          t.stringLiteral(importFilePath.replace('/src', '..')),
        ),
      );
    },
    ArrayExpression(path) {
      const newNode = t.objectExpression([
        t.objectProperty(t.identifier('path'), t.stringLiteral(routePath)),
        t.objectProperty(t.identifier('component'), t.identifier(component)),
      ]);
      path.node.elements.push(newNode);
    },
  });
  return ast;
}