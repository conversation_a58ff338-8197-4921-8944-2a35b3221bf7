# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.4.5](https://github.com/netease/tango/compare/@music163/tango-ui@1.4.4...@music163/tango-ui@1.4.5) (2024-09-09)

### Bug Fixes

- add classNameSetter and classNameInput ([#207](https://github.com/netease/tango/issues/207)) ([55602fc](https://github.com/netease/tango/commit/55602fc961b5c20d923891ce13d7a51588bd047f))

## [1.4.4](https://github.com/netease/tango/compare/@music163/tango-ui@1.4.3...@music163/tango-ui@1.4.4) (2024-09-02)

### Bug Fixes

- add prop value template & fix components popover ([#201](https://github.com/netease/tango/issues/201)) ([c4e1ed6](https://github.com/netease/tango/commit/c4e1ed67f55deb17dae740559602cd0adcfc8eb4))

## [1.4.3](https://github.com/netease/tango/compare/@music163/tango-ui@1.4.2...@music163/tango-ui@1.4.3) (2024-08-16)

### Bug Fixes

- CodeInput 支持 jsx css 代码补全 ([#198](https://github.com/netease/tango/issues/198)) ([6de5488](https://github.com/netease/tango/commit/6de54884b50d683c74d695a8c13184dc0ce8d3c4))

## [1.4.2](https://github.com/netease/tango/compare/@music163/tango-ui@1.4.1...@music163/tango-ui@1.4.2) (2024-08-09)

### Bug Fixes

- route matching without query string & toggle button wrapping ([#195](https://github.com/netease/tango/issues/195)) ([9394666](https://github.com/netease/tango/commit/939466648d40331800f15e886256a0789b7e7623))
- update mobile simulator ui ([#196](https://github.com/netease/tango/issues/196)) ([65149f1](https://github.com/netease/tango/commit/65149f1326eaf78e8a27222993c3cacc674cd378))

## [1.4.1](https://github.com/netease/tango/compare/@music163/tango-ui@1.4.0...@music163/tango-ui@1.4.1) (2024-08-05)

**Note:** Version bump only for package @music163/tango-ui

# [1.4.0](https://github.com/netease/tango/compare/@music163/tango-ui@1.3.3...@music163/tango-ui@1.4.0) (2024-08-01)

### Bug Fixes

- update types ([67d602a](https://github.com/netease/tango/commit/67d602a3ec6b7f74156bb78fda6f3b4bc2676e55))

### Features

- add isError and errorMessage to File & add isAstSynced to Module ([#190](https://github.com/netease/tango/issues/190)) ([8dd289c](https://github.com/netease/tango/commit/8dd289cd7daba628e54dc6b8929f22a1e2245160))

## [1.3.3](https://github.com/netease/tango/compare/@music163/tango-ui@1.3.2...@music163/tango-ui@1.3.3) (2024-07-12)

### Bug Fixes

- selection bug without prototype & update ToggleButton style ([#180](https://github.com/netease/tango/issues/180)) ([1d0fb4f](https://github.com/netease/tango/commit/1d0fb4f274a5b767962c54583ab5863329d5bcc5))

## [1.3.2](https://github.com/netease/tango/compare/@music163/tango-ui@1.3.1...@music163/tango-ui@1.3.2) (2024-06-20)

### Bug Fixes

- drag-panel add resizeable & height prop ([#175](https://github.com/netease/tango/issues/175)) ([b53a9f7](https://github.com/netease/tango/commit/b53a9f7eefc748ba8a7c94270c3a374b9d6ed211))
- update designer style ([#176](https://github.com/netease/tango/issues/176)) ([0f3f0af](https://github.com/netease/tango/commit/0f3f0afdfa8aee2532a97c5c2e92ef4230397d86))

## [1.3.1](https://github.com/netease/tango/compare/@music163/tango-ui@1.3.0...@music163/tango-ui@1.3.1) (2024-06-05)

### Bug Fixes

- fix context menu error on macOS ([#170](https://github.com/netease/tango/issues/170)) ([ca740c8](https://github.com/netease/tango/commit/ca740c83ae7f72a75c7db90d2e137a7e9a032326))

# [1.3.0](https://github.com/netease/tango/compare/@music163/tango-ui@1.2.3...@music163/tango-ui@1.3.0) (2024-06-03)

### Features

- add context menu ([#161](https://github.com/netease/tango/issues/161)) ([28040fc](https://github.com/netease/tango/commit/28040fc00604339a40ad3216b76baf7de93a13e0))

## [1.2.3](https://github.com/netease/tango/compare/@music163/tango-ui@1.2.2...@music163/tango-ui@1.2.3) (2024-05-30)

### Bug Fixes

- popover re-click position bug & drag panel drag bug ([#165](https://github.com/netease/tango/issues/165)) ([f83c6bc](https://github.com/netease/tango/commit/f83c6bc0f69820582720512eefedc9ddf5db2975))

## [1.2.2](https://github.com/netease/tango/compare/@music163/tango-ui@1.2.1...@music163/tango-ui@1.2.2) (2024-05-27)

### Bug Fixes

- export usePreviewSandboxQuery & add builtin sandboxQuery ([#163](https://github.com/netease/tango/issues/163)) ([163eced](https://github.com/netease/tango/commit/163ecedff3ed7baee59e200a8cc60dcc63a24e48))

## [1.2.1](https://github.com/netease/tango/compare/@music163/tango-ui@1.2.0...@music163/tango-ui@1.2.1) (2024-05-22)

**Note:** Version bump only for package @music163/tango-ui

# [1.2.0](https://github.com/netease/tango/compare/@music163/tango-ui@1.1.0...@music163/tango-ui@1.2.0) (2024-05-21)

### Bug Fixes

- support add components with popover ([#155](https://github.com/netease/tango/issues/155)) ([f17ccbb](https://github.com/netease/tango/commit/f17ccbb7f645f8047ecd96d9f3f2185048a3b726))

### Features

- event-setter & expression-setter use popover ([#159](https://github.com/netease/tango/issues/159)) ([9daf387](https://github.com/netease/tango/commit/9daf3872e743fcf706184877020bcbf1f75ffd25))

# [1.1.0](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.3...@music163/tango-ui@1.1.0) (2024-05-17)

### Features

- refactor parse attribute value ([#149](https://github.com/netease/tango/issues/149)) ([ffaa276](https://github.com/netease/tango/commit/ffaa276b5c205ed962d37e2fdb358a703f8fad01))

## [1.0.2](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.1...@music163/tango-ui@1.0.2) (2024-04-22)

### Bug Fixes

- allow reload state tree & display pageStore in variable tree ([#135](https://github.com/netease/tango/issues/135)) ([2664613](https://github.com/netease/tango/commit/2664613ab263aead2a5239fa012454fc7fd5ff99))

## [1.0.1](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0...@music163/tango-ui@1.0.1) (2024-04-16)

**Note:** Version bump only for package @music163/tango-ui

# [1.0.0-alpha.12](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.11...@music163/tango-ui@1.0.0-alpha.12) (2024-04-09)

### Bug Fixes

- update setters and use tabOptions to filter props ([#129](https://github.com/netease/tango/issues/129)) ([93608d1](https://github.com/netease/tango/commit/93608d1037327afa4f755976b86427b6128ae3d0))

# [1.0.0-alpha.11](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.10...@music163/tango-ui@1.0.0-alpha.11) (2024-03-26)

**Note:** Version bump only for package @music163/tango-ui

# [1.0.0-alpha.10](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.9...@music163/tango-ui@1.0.0-alpha.10) (2024-03-19)

### Bug Fixes

- select outline node & render dependency list ([#118](https://github.com/netease/tango/issues/118)) ([07febb3](https://github.com/netease/tango/commit/07febb385f710f9a0bc43639ce22787bb05e97ed))

# [1.0.0-alpha.9](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.8...@music163/tango-ui@1.0.0-alpha.9) (2024-03-18)

**Note:** Version bump only for package @music163/tango-ui

# [1.0.0-alpha.8](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.7...@music163/tango-ui@1.0.0-alpha.8) (2024-03-18)

### Bug Fixes

- refactor action, formHeader, selectionDropdown ([#114](https://github.com/netease/tango/issues/114)) ([489118b](https://github.com/netease/tango/commit/489118b88aedc6672e2387f795253f94bcdf6f9b))

# [1.0.0-alpha.7](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.6...@music163/tango-ui@1.0.0-alpha.7) (2024-01-29)

### Bug Fixes

- remove load iconfont ([#97](https://github.com/netease/tango/issues/97)) ([d5e42c9](https://github.com/netease/tango/commit/d5e42c93bb79362dd5f15d5cd2147952dee681bf))

# [1.0.0-alpha.6](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.5...@music163/tango-ui@1.0.0-alpha.6) (2024-01-24)

### Bug Fixes

- update service preview panel ([#94](https://github.com/netease/tango/issues/94)) ([d6a08ee](https://github.com/netease/tango/commit/d6a08eecd2521e9699bee1ff3ffebb651a2a620d))

# [1.0.0-alpha.5](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.4...@music163/tango-ui@1.0.0-alpha.5) (2024-01-23)

### Bug Fixes

- update FormObject style ([#91](https://github.com/netease/tango/issues/91)) ([8fd085d](https://github.com/netease/tango/commit/8fd085d4fd41e588a5d3657add736a1e56088e4a))

# [1.0.0-alpha.4](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.3...@music163/tango-ui@1.0.0-alpha.4) (2024-01-16)

**Note:** Version bump only for package @music163/tango-ui

# [1.0.0-alpha.3](https://github.com/netease/tango/compare/@music163/tango-ui@1.0.0-alpha.2...@music163/tango-ui@1.0.0-alpha.3) (2023-12-29)

**Note:** Version bump only for package @music163/tango-ui

# [1.0.0-alpha.1](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.12...@music163/tango-ui@1.0.0-alpha.1) (2023-12-12)

### Bug Fixes

- add showSearch and showGroups to SettingForm & update internal icons ([#73](https://github.com/netease/tango/issues/73)) ([a8357f4](https://github.com/netease/tango/commit/a8357f45b8b0c1f2b91a856918f6bdecdc65aa73))
- disable search keymap of InputCode ([#72](https://github.com/netease/tango/issues/72)) ([9db6358](https://github.com/netease/tango/commit/9db635858876ee0b9612e4b2ccd996280fc35d3b))
- render form item without setter & enhance tools ([#76](https://github.com/netease/tango/issues/76)) ([1acfa68](https://github.com/netease/tango/commit/1acfa6864b1faad1a441facc426d8d94b6b090b5))

## [0.1.12](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.11...@music163/tango-ui@0.1.12) (2023-11-28)

### Bug Fixes

- update theme ([2550411](https://github.com/netease/tango/commit/2550411c1c93037931d44aa9f7822ffe8caa900b))

## [0.1.11](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.10...@music163/tango-ui@0.1.11) (2023-11-23)

**Note:** Version bump only for package @music163/tango-ui

## [0.1.10](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.9...@music163/tango-ui@0.1.10) (2023-11-20)

**Note:** Version bump only for package @music163/tango-ui

## [0.1.9](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.8...@music163/tango-ui@0.1.9) (2023-11-16)

**Note:** Version bump only for package @music163/tango-ui

## [0.1.8](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.7...@music163/tango-ui@0.1.8) (2023-11-02)

### Bug Fixes

- outline-panel tree expand bug & add props ([#52](https://github.com/netease/tango/issues/52)) ([b29e235](https://github.com/netease/tango/commit/b29e23599befbd60845053544648b10976a65824))

## [0.1.7](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.6...@music163/tango-ui@0.1.7) (2023-10-23)

### Bug Fixes

- enhance expSetter ([#43](https://github.com/netease/tango/issues/43)) ([5ebbb42](https://github.com/netease/tango/commit/5ebbb428fb3fb786d330ab01959028443338d315))

## [0.1.6](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.5...@music163/tango-ui@0.1.6) (2023-10-12)

### Bug Fixes

- refactor expSetter ([#41](https://github.com/netease/tango/issues/41)) ([d63517e](https://github.com/netease/tango/commit/d63517ecb936e4227e70c33e610664316625f4f4))

## [0.1.5](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.4...@music163/tango-ui@0.1.5) (2023-10-09)

### Bug Fixes

- update tabs style ([46b5445](https://github.com/netease/tango/commit/46b54459ceb23f9df52b9eb40dc935bc872ba1b4))

## [0.1.4](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.3...@music163/tango-ui@0.1.4) (2023-09-18)

**Note:** Version bump only for package @music163/tango-ui

## [0.1.3](https://github.com/netease/tango/compare/@music163/tango-ui@0.1.2...@music163/tango-ui@0.1.3) (2023-09-13)

### Bug Fixes

- update exports ([443ae58](https://github.com/netease/tango/commit/443ae589f67255f3815b587fd8f928c2b8742e9a))

## 0.1.2 (2023-09-06)

**Note:** Version bump only for package @music163/tango-ui
