const packageJson = {
  name: 'demo',
  private: true,
  dependencies: {
    '@music163/antd': '0.2.5',
    '@music163/tango-boot': '0.2.5',
    react: '17.0.2',
    'react-dom': '17.0.2',
    'prop-types': '15.7.2',
    tslib: '2.5.0',
    '@emotion/react': '^11.14.0',
    '@emotion/styled': '^11.14.0',
    '@mui/icons-material': '^5.16.12 || ^6.0.0',
    '@mui/material': '^5.16.12 || ^6.0.0',
    'ra-core': '^5.6.2',
    'ra-i18n-polyglot': '^5.6.2',
    'ra-language-english': '^5.6.2',
    'ra-ui-materialui': '^5.6.2',
    'react-hook-form': '^7.53.0',
    'react-router': '^6.28.1 || ^7.1.1',
    'react-router-dom': '^6.28.1 || ^7.1.1',
    execa: '^5.1.1',
    'fs-extra': '^11.1.1',
    ink: '^5.0.0',
    'ink-select-input': '^6.0.0',
    'ink-text-input': '^6.0.0',
    lodash: '~4.17.5',
    meow: '^9.0.0',
  },
  devDependencies: {
    'cross-env': '^5.2.0',
    expect: '^27.4.6',
    'react-router': '^6.28.1',
    'react-router-dom': '^6.28.1',
    rimraf: '^3.0.2',
    typescript: '^5.1.3',
  },
};

const tangoConfigJson = {
  designerConfig: {
    autoGenerateComponentId: true,
  },
  packages: {
    react: {
      version: '17.0.2',
      library: 'React',
      type: 'dependency',
      resources: ['https://unpkg.com/react@{{version}}/umd/react.development.js'],
    },
    'react-dom': {
      version: '17.0.2',
      library: 'ReactDOM',
      type: 'dependency',
      resources: ['https://unpkg.com/react-dom@{{version}}/umd/react-dom.development.js'],
    },
    'react-is': {
      version: '16.13.1',
      library: 'ReactIs',
      type: 'dependency',
      resources: ['https://unpkg.com/react-is@{{version}}/umd/react-is.production.min.js'],
    },
    'styled-components': {
      version: '5.3.5',
      library: 'styled',
      type: 'dependency',
      resources: ['https://unpkg.com/styled-components@{{version}}/dist/styled-components.min.js'],
    },
    moment: {
      version: '2.29.4',
      library: 'moment',
      type: 'dependency',
      resources: ['https://unpkg.com/moment@{{version}}/moment.js'],
    },
    '@music163/tango-boot': {
      description: '云音乐低代码运行时框架',
      version: '0.2.5',
      library: 'TangoBoot',
      type: 'baseDependency',
      resources: ['https://unpkg.com/@music163/tango-boot@{{version}}/dist/boot.js'],
      // resources: ['http://localhost:9001/boot.js'],
    },
    '@music163/antd': {
      description: '云音乐低代码中后台应用基础物料',
      version: '0.2.5',
      library: 'TangoAntd',
      type: 'baseDependency',
      resources: [
        'https://unpkg.com/@music163/antd@{{version}}/dist/index.js',
        // 'http://localhost:9002/designer.js',
        'https://unpkg.com/antd@4.24.13/dist/antd.css',
      ],
      designerResources: [
        'https://unpkg.com/@music163/antd@{{version}}/dist/designer.js',
        'https://unpkg.com/antd@4.24.13/dist/antd.css',
      ],
    },
  },
};

const helperCode = `
export function registerComponentPrototype(proto) {
  if (!proto) return;
  if (!window.localTangoComponentPrototypes) {
    window.localTangoComponentPrototypes = {};
  }
  if (proto.name) {
    window.localTangoComponentPrototypes[proto.name] = proto;
  }
}
`;

const routesCode = `
import Index from "./pages/list";
import Detail from "./pages/detail";
import Counter from "./pages/counter";
import Fun from "./pages/fun";

const routes = [
  {
    path: '/',
    exact: true,
    component: Index
  },
  {
    path: '/detail',
    exact: true,
    component: Detail
  },
  {
    path: '/counter',
    exact: true,
    component: Counter
  },
  {
    path: '/fun',
    exact: true,
    component: Fun
  }
];

export default routes;
`;

const storeIndexCode = `
export { default as app } from './app';
export { default as counter } from './counter';
`;

const entryCode = `
import { runApp } from '@music163/tango-boot';
import routes from './routes';
import './services';
import './stores';
import './index.less';

runApp({
  boot: {
    mountElement: document.querySelector('#root'),
    qiankun: false,
  },

  router: {
    type: 'browser',
    config: routes,
  },
});
`;

const storeCounter = `
import { defineStore } from '@music163/tango-boot';

const counter = defineStore({
  // state
  num: 0,

  // action
  increment: () => counter.num++,

  decrement: () => {
    counter.num--;
  },
}, 'counter');

export default counter;
`;

const viewHomeCode = `
import React from "react";
import { definePage } from "@music163/tango-boot";
import {
  Page,
  Section,
  Button,
  Input,
  FormilyForm,
  FormilyFormItem,
  Table,
} from "@music163/antd";
import { Space } from "@music163/antd";
import { LocalButton } from "../components";
class App extends React.Component {
  render() {
    return (
      <Page title={tango.stores.app.title} subTitle={111}>
        <Section tid="section0" />
        <Section tid="section1" title="Section Title">
          your input: <Input tid="input1" defaultValue="hello" />
          copy input: <Input value={tango.page.input1?.value} />
          <Table
          columns={[
            { title: "姓名", dataIndex: "name", key: "name" },
            { title: "年龄", dataIndex: "age", key: "age" },
            { title: "住址", dataIndex: "address", key: "address" },
          ]}
          tid="table1"
        />
        </Section>
        <Section tid="section2">
          <Space tid="space1">
            <LocalButton />
            <Button tid="button1">button</Button>
          </Space>
        </Section>
        <Section title="区块标题" tid="section3">
          <FormilyForm tid="formilyForm1">
            <FormilyFormItem name="input1" component="Input" label="表单项" />
            <FormilyFormItem name="select1" component="Select" label="表单项" />
          </FormilyForm>
        </Section>
        <Section title="原生 DOM" tid="section4">
          <h1 style={{ ...{ color: "red" }, fontSize: 64 }}>
            hello world
          </h1>
          <div
            style={{
              border: "1px solid #ccc",
              borderRadius: "5px",
              backgroundColor: "#f4f4f4",
            }}
          >
            <form onSubmit={tango.stores.app.submitForm} style={{
              padding: "10px",
            }}>
              <div>
                <label>Username:</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  style={{ margin: "5px" }}
                />
              </div>
              <div>
                <label>Password:</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  style={{ margin: "5px" }}
                />
              </div>
              <div>
                <label>Role:</label>
                <select id="role" name="role" style={{ margin: "5px" }}>
                  <option value="admin">Admin</option>
                  <option value="user">User</option>
                </select>
              </div>
              <div>
                <button
                  type="submit"
                  style={{
                    marginTop: '5px',
                    padding: "3px 10px",
                    backgroundColor: "#007bff",
                    color: "#fff",
                    border: "none",
                    borderRadius: "5px",
                  }}
                >
                  Submit
                </button>
              </div>
            </form>
          </div>
        </Section>
      </Page>
    );
  }
}
export default definePage(App);
`;

const funPageCode = `
import React from 'react';
import { definePage } from '@music163/tango-boot';
import { Button } from '../components';

function App() {
  return (
    <div>
      <p>hello world</p>
      <Button onClick={() => tango.navigateTo('/')}>切换到首页</Button>
    </div>
  );
}

export default definePage(App);
`;

const counterPageCode = `
import React from 'react';
import { definePage } from '@music163/tango-boot';
import { Layout, Card, Text, Box } from '@music163/antd';
import { Input, Button } from '../components';

class App extends React.Component {
  render() {
    return (
      <Layout style={{ minHeight: '100vh', gap: 24, padding: 24 }}>
        <Button type="primary" onClick={() => tango.navigateTo('/fun')}>
          切换路由 /fun
        </Button>
        <Box>
          <Text>hello</Text>
        </Box>
        <Card title="计数器">
          <h1>{tango.stores.counter.number}</h1>
          <button onClick={tango.stores.counter.add}>+</button>
          <button onClick={tango.stores.counter.decrement}>-</button>
        </Card>
        <Card title="通过 tid 实现状态控制">
          <Input tid="input1" defaultValue="hello world" />
          <div>输入框的值为：{tango.pageStore.input1?.value}</div>
          <Button
            tid="button1"
            onClick={() => {
              tango.pageStore.input1?.setValue('hello');
            }}
          >
            setValue
          </Button>
        </Card>
        <Card title="异步请求">
          <Button
            onClick={async () => {
              await tango.stores.dogs?.listAllBreeds();
            }}
          >
            加载dogs列表
          </Button>
          <div>
            <div>
              {Object.keys(tango.stores.dogs?.list || {}).map((item) => (
                <button
                  type="button"
                  key={item}
                  onClick={async () => {
                    await tango.stores.dogs?.getRandomImage(item);
                  }}
                >
                  {item}
                </button>
              ))}
            </div>
            {tango.stores.dogs?.image ? (
              <div>
                <img src={tango.stores.dogs?.image} alt="breed image" />
              </div>
            ) : (
              <div>点击上面的 dog name，加载图片</div>
            )}
          </div>
        </Card>
      </Layout>
    );
  }
}

export default definePage(App);
`;

export const reactEmptyPageCode = `
import React from "react";
import { definePage } from "@music163/tango-boot";
import {
  Page,
  Section,
} from "@music163/antd";

function App() {
  return (<Page title="Detail Page">
    <Section></Section>
  </Page>)
}

export default definePage(App);
`;

const componentsButtonCode = `
import React from 'react';
import { registerComponentPrototype } from '../utils';

export default function MyButton(props) {
  return <button {...props}>my button</button>
}

registerComponentPrototype({
  name: 'LocalButton',
  title: 'Local Button',
  exportType: 'namedExport',
  package: '/src/components',
  props: [
    { name: 'background', title: '背景色', setter: 'colorSetter'  },
  ],
});
`;

const componentsInputCode = `
import React from 'react';
import { registerComponentPrototype } from '../utils';

export default function MyInput(props) {
  return <input {...props} />;
}

registerComponentPrototype({
  name: 'LocalInput',
  title: 'Local Input',
  exportType: 'namedExport',
  package: '/src/components',
  props: [
    { name: 'color', title: '文本色', setter: 'colorSetter'  },
  ],
});
`;

const componentsEntryCode = `
export { default as LocalButton } from './button';
export { default as LocalInput } from './input';
`;

const storeApp = `
import { defineStore } from '@music163/tango-boot';

export default defineStore({
  title: 'Page Title',

  array: [1, 2, 3],

  test: function() {
    console.log('test');
  },

  submitForm: function(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const username = formData.get("username");
    const password = formData.get("password");
    const role = formData.get("role");
    console.log("Submitted Data:", { username, password, role });
  }
}, 'app');
`;

const serviceCode = `
import { defineServices } from '@music163/tango-boot';
import './sub';

export default defineServices({
  longLongLongLongLongLongLongLongGet: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/cc974ffbaa7a85c77f30e4ce67deb67f/api/getUserProfile',
    formatter: res => res.data,
    headers: {
      'Content-Type': 'application/json',
    }
  },
  get: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/cc974ffbaa7a85c77f30e4ce67deb67f/api/getUserProfile',
    formatter: res => res.data,
    headers: {
      'Content-Type': 'application/json',
    }
  },
  list: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/c45109399a1d33d83e32a59984b25b00/anchor-list-normal',
  },
  add: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/c45109399a1d33d83e32a59984b25b00/api/users',
    method: 'post',
  },
  update: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/c45109399a1d33d83e32a59984b25b00/api/users',
    method: 'post',
  },
  delete: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/c45109399a1d33d83e32a59984b25b00/api/users?id=1',
  },
});
`;

const subServiceCode = `
import { defineServices } from '@music163/tango-boot';

export default defineServices({
  list: {
    url: 'https://nei.hz.netease.com/api/apimock-v2/c45109399a1d33d83e32a59984b25b00/anchor-list-normal',
  },
}, {
  namespace: 'sub',
});
`;

const lessCode = `
body {
  font-size: 12px;
}
`;

const cssCode = `
* {
  margin: 0;
  padding: 0;
}

p {
  color: red;
}
`;

export const reactSampleFiles = [
  { filename: '/package.json', code: JSON.stringify(packageJson) },
  { filename: '/tango.config.json', code: JSON.stringify(tangoConfigJson) },
  { filename: '/README.md', code: '# readme' },
  { filename: '/src/index.less', code: lessCode },
  { filename: '/src/style.css', code: cssCode },
  { filename: '/src/index.js', code: entryCode },
  { filename: '/src/pages/list.js', code: viewHomeCode },
  { filename: '/src/pages/fun.js', code: funPageCode },
  { filename: '/src/pages/counter.js', code: counterPageCode },
  { filename: '/src/pages/detail.js', code: reactEmptyPageCode },
  { filename: '/src/components/button.js', code: componentsButtonCode },
  { filename: '/src/components/input.js', code: componentsInputCode },
  { filename: '/src/components/index.js', code: componentsEntryCode },
  { filename: '/src/routes.js', code: routesCode },
  { filename: '/src/stores/index.js', code: storeIndexCode },
  { filename: '/src/stores/app.js', code: storeApp },
  { filename: '/src/stores/counter.js', code: storeCounter },
  { filename: '/src/services/index.js', code: serviceCode },
  { filename: '/src/services/sub.js', code: subServiceCode },
  { filename: '/src/utils/index.js', code: helperCode },
];

export const genReactDefaultPage = (index: number) => ({
  name: 'new-page',
  code: `
  import React from 'react';
  import tango, { definePage } from '@music163/tango-boot';
  import { Page, Section } from '@music163/antd';

  function App() {
    return (
      <Page title="空白模板${index}">
        <Section></Section>
      </Page>
    )
  }

  export default definePage(App);
  `,
});
