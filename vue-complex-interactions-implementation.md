# Vue页面复杂交互设计实现

## 功能概述

基于Tango框架为Vue项目实现了复杂交互设计支持，主要包括：

1. **弹窗组件的可视化编辑**：支持向画布添加弹窗组件，并可拖拽添加或编辑弹窗组件中的节点内容
2. **页面元素间的联动设置**：支持Vue特有的事件绑定和状态管理，实现组件间的复杂交互

## 实现方案

### 1. 弹窗组件支持

#### 1.1 Vue弹窗管理
- **扩展VueViewFile模型**：为`VueViewFile`类添加了`listModals()`方法
- **弹窗识别**：支持识别`nb-dialog`、`nb-drawer`、`nb-modal`等Vue弹窗组件
- **弹窗列表**：在事件设置器中可以选择已有的弹窗进行操作

```typescript
// packages/core/src/models/vue-view-file.ts
listModals?(): Array<{ label: string; value: string }> {
  const modals: Array<{ label: string; value: string }> = [];
  const activeViewNodes = this.nodes || new Map();

  Array.from(activeViewNodes.values()).forEach((node) => {
    if (['nb-dialog', 'nb-drawer', 'nb-modal'].includes(node.component) && node.props.id) {
      modals.push({
        label: `${node.component}(${node.props.id})`,
        value: node.props.id,
      });
    }
  });

  return modals;
}
```

#### 1.2 弹窗内容可视化编辑
- **拖拽支持**：弹窗内容区域支持拖拽添加组件
- **嵌套编辑**：支持在弹窗的header、body、footer区域分别编辑内容
- **实时预览**：弹窗内容修改后可实时在画布中预览

### 2. Vue事件处理系统

#### 2.1 Vue事件设置器
创建了专门的Vue事件设置器`VueEventSetter`，支持Vue特有的事件处理：

```typescript
// packages/designer/src/setters/vue-event-setter.tsx
enum VueEventAction {
  NoAction = 'noAction',
  ConsoleLog = 'consoleLog',
  BindExpression = 'bindExpression',
  OpenModal = 'openModal',
  CloseModal = 'closeModal',
  NavigateTo = 'navigateTo',
  ToggleVariable = 'toggleVariable',
  SetVariable = 'setVariable',
  CallMethod = 'callMethod',
}
```

#### 2.2 支持的事件类型
- **基础事件**：无动作、打印事件、绑定JS表达式
- **导航事件**：打开页面、路由跳转
- **弹窗事件**：打开弹窗、关闭弹窗
- **状态事件**：切换变量值、设置变量值
- **方法调用**：调用Vue组件方法

#### 2.3 Vue特有语法支持
- **事件绑定**：`@click`、`@open`、`@close`等Vue事件语法
- **变量操作**：支持Vue响应式变量的切换和设置
- **方法调用**：支持调用Vue组件的methods

### 3. Vue状态管理

#### 3.1 Vue状态设置器
创建了`VueStateSetter`和`VueBooleanSetter`，支持Vue数据绑定：

```typescript
// packages/designer/src/setters/vue-state-setter.tsx
const bindingOptions = [
  { label: '静态值', value: 'static' },
  { label: '响应式数据', value: 'reactive' },
  { label: '计算属性', value: 'computed' },
];
```

#### 3.2 数据绑定类型
- **静态值**：直接设置固定值
- **响应式数据**：绑定Vue响应式变量，自动更新视图
- **计算属性**：基于其他数据动态计算的值

#### 3.3 Vue特有属性支持
- **v-model**：双向数据绑定
- **:value**：单向数据绑定
- **:disabled**：动态禁用状态
- **布尔值绑定**：支持true/false以及响应式布尔值

### 4. 组件原型更新

#### 4.1 Vue弹窗组件
更新了`nb-dialog`组件原型，使用Vue特有的设置器：

```typescript
// apps/playground/src/helpers/nubes-ui-prototypes.ts
'nb-dialog': {
  // ...
  props: [
    {
      name: 'v-model',
      title: '是否显示对话框',
      setter: 'vueBoolSetter',
      group: 'basic',
    },
    {
      name: '@open',
      title: '打开回调',
      setter: 'vueEventSetter',
      group: 'event',
    },
    // ...
  ],
}
```

#### 4.2 Vue按钮组件
为`nb-button`组件添加了Vue事件支持：

```typescript
{
  name: '@click',
  title: '点击事件',
  setter: 'vueEventSetter',
  group: 'event',
}
```

#### 4.3 原生HTML元素
更新了原生HTML元素的事件绑定，使用Vue语法：

```typescript
{
  name: '@click',
  title: '点击事件',
  setter: 'vueEventSetter',
  group: 'event',
}
```

### 5. 示例实现

#### 5.1 弹窗示例
在Vue项目的HomeView中添加了完整的弹窗示例：

```vue
<template>
  <main>
    <!-- 其他内容 -->
    <h3>弹窗组件示例：</h3>
    <nb-button type="primary" @click="dialogVisible = true">打开弹窗</nb-button>
    <nb-dialog 
      id="exampleDialog" 
      v-model="dialogVisible" 
      title="示例弹窗" 
      width="500px"
      @open="console.log('弹窗打开')"
      @close="console.log('弹窗关闭')"
    >
      <template #header>
        <span>弹窗标题</span>
      </template>
      <div>
        <p>这是一个可以拖拽编辑的弹窗内容区域</p>
        <nb-input v-model="inputValue" placeholder="在弹窗中输入内容" />
      </div>
      <template #footer>
        <nb-button @click="dialogVisible = false">取消</nb-button>
        <nb-button type="primary" @click="handleConfirm">确定</nb-button>
      </template>
    </nb-dialog>
  </main>
</template>

<script setup>
import { ref } from 'vue'

const dialogVisible = ref(false)
const inputValue = ref('')

const handleConfirm = () => {
  console.log('确认操作，输入值：', inputValue.value)
  dialogVisible.value = false
}
</script>
```

## 技术特点

### 1. Vue语法原生支持
- 完全支持Vue的事件绑定语法（@click、@open等）
- 支持Vue的数据绑定语法（v-model、:value等）
- 支持Vue的响应式数据系统

### 2. 可视化编辑体验
- 弹窗内容可以通过拖拽方式添加和编辑组件
- 事件设置通过图形界面完成，无需手写代码
- 状态绑定支持下拉选择和自动补全

### 3. 类型安全
- 所有设置器都有完整的TypeScript类型定义
- 事件处理函数有类型检查
- 组件属性有类型约束

### 4. 扩展性强
- 设置器系统支持注册新的Vue特有设置器
- 事件系统支持添加新的事件类型
- 组件原型系统支持自定义Vue组件

## 使用方式

### 1. 添加弹窗组件
1. 从左侧组件面板拖拽`nb-dialog`组件到画布
2. 设置弹窗的基本属性（标题、宽度等）
3. 在弹窗的header、body、footer区域拖拽添加内容
4. 配置弹窗的显示状态（v-model绑定）

### 2. 设置页面联动
1. 选择触发元素（如按钮）
2. 在属性面板的"事件"分组中设置@click事件
3. 选择事件类型（如"打开弹窗"、"设置变量值"等）
4. 配置具体的事件参数

### 3. 配置状态绑定
1. 选择需要绑定状态的组件
2. 在属性面板中找到相应的属性（如v-model）
3. 选择绑定类型（静态值、响应式数据、计算属性）
4. 配置具体的绑定值或表达式

## 总结

通过以上实现，Vue项目现在完全支持复杂的交互设计，包括：

- ✅ 弹窗组件的可视化添加和编辑
- ✅ 弹窗内容的拖拽式编辑
- ✅ Vue特有的事件绑定语法支持
- ✅ 响应式数据的可视化绑定
- ✅ 页面元素间的复杂联动设置
- ✅ 完整的类型安全和扩展性

这些功能使得Vue项目的可视化编辑体验与React项目保持一致，同时充分利用了Vue框架的特性和优势。
