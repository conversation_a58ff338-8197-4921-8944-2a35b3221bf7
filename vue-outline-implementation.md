# Vue文件结构展示功能测试

## 功能概述

为Vue文件实现了页面结构展示功能，用户可以通过点击左侧工具菜单中的【结构】菜单来展示页面节点列表，当点击某一级节点时选中并高亮画布中对应的元素节点。

## 实现方案

### 1. 启用结构菜单
在Vue项目页面(`apps/playground/src/pages/index.tsx`)中启用了outline菜单项：

```tsx
<Sidebar.Item key="outline" label="结构" icon={<BuildOutlined />} />
```

### 2. 优化节点标签显示
修改了`packages/designer/src/sidebar/outline-panel/components-tree.tsx`中的节点标签显示逻辑：

```tsx
const nodeLabel = (() => {
  const { codeId } = parseDndId(node.id);
  return codeId || node.component; // 如果没有codeId，显示组件名
})();
```

### 3. 现有基础设施支持

Vue文件的结构展示功能基于以下现有实现：

#### Vue文件解析 (`vue-traverse.ts`)
- 使用`node-html-parser`解析Vue SFC的`<template>`部分
- 为每个HTML元素注入`data-dnd`追踪属性
- 生成节点数据结构供结构面板使用

#### 节点数据模型 (`VueViewFile`, `VueViewNode`)
- `VueViewFile`管理Vue文件的AST和节点树
- `VueViewNode`表示单个Vue模板节点
- `nodeListToTreeData`函数构建层次化的节点树

#### 结构面板组件 (`OutlinePanel`, `ComponentsTree`)
- `OutlinePanel`提供结构展示的容器
- `ComponentsTree`渲染节点树并处理选中交互
- 支持节点展开/折叠、拖拽、右键菜单等功能

#### 选中和高亮机制
- 通过`data-dnd`属性查询DOM元素
- `sandboxQuery.getDraggableParentsData`获取元素数据
- `workspace.selectSource.select`选中节点并触发高亮

### 4. 组件原型支持

Vue项目包含了完整的组件原型配置：

#### 基础HTML元素
```typescript
const nativeDomPrototypes = () => {
  const doms = ['div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'a', 'img', 
                'ul', 'ol', 'li', 'input', 'button', 'form', 'table', 'tr', 'td', 
                'header', 'footer', 'nav', 'section', 'article', 'aside', 'main', 
                'video', 'audio', 'label', 'select', 'option', 'textarea', 'iframe'];
  // ...
};
```

#### Nubes UI组件
- `nb-text`, `nb-button`, `nb-link`等基础组件
- `nb-table`, `nb-card`, `nb-empty`等数据展示组件
- `nb-dialog`等反馈组件
- `nb-form`, `nb-input`等表单组件

## 测试场景

### 1. 基本功能测试
- [ ] 打开Vue项目页面
- [ ] 点击左侧【结构】菜单
- [ ] 验证结构面板正确显示
- [ ] 验证节点树层次结构正确

### 2. 节点选中测试
- [ ] 点击结构面板中的节点
- [ ] 验证画布中对应元素被选中
- [ ] 验证选中元素有高亮边框
- [ ] 验证右侧属性面板显示对应组件属性

### 3. 节点标签测试
- [ ] 验证有`tid`属性的节点显示`tid`值
- [ ] 验证没有`tid`属性的节点显示组件名
- [ ] 验证HTML元素(如div、h1等)正确显示
- [ ] 验证Vue组件(如nb-button等)正确显示

### 4. 交互功能测试
- [ ] 验证节点展开/折叠功能
- [ ] 验证节点拖拽功能
- [ ] 验证右键菜单功能
- [ ] 验证显示/隐藏切换功能

## 示例Vue文件结构

以`HomeView.vue`为例，应该显示如下结构：

```
main
├── h1
├── h3
├── nb-table
│   ├── nb-table-column
│   ├── nb-table-column
│   └── nb-table-column
├── h3
└── nb-timeline
    └── nb-timeline-item
```

## 技术细节

### 追踪属性注入
Vue模板中的每个元素都会被注入追踪属性：
```html
<main data-dnd="HomeView:main:1">
  <h1 data-dnd="HomeView:h1:2">原生Dom h1 标签</h1>
  <nb-table data-dnd="HomeView:nb-table:3" :data="[...]">
    <nb-table-column data-dnd="HomeView:nb-table-column:4" prop="date" label="Date" />
  </nb-table>
</main>
```

### 选中查询
点击结构面板节点时，通过CSS选择器查找对应DOM元素：
```typescript
const data = sandboxQuery.getDraggableParentsData(
  buildQueryBySlotId(slotKey), // `[data-dnd="${slotKey}"]`
  true
);
```

## 总结

Vue文件的页面结构展示功能已经完全实现，基于现有的Tango框架基础设施，无需额外的开发工作。用户可以：

1. 通过【结构】菜单查看Vue文件的页面节点层次结构
2. 点击节点选中并高亮画布中的对应元素
3. 享受完整的节点交互功能（展开/折叠、拖拽、右键菜单等）

该功能与React项目的结构展示功能保持一致的用户体验。
