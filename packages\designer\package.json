{"name": "@music163/tango-designer", "version": "1.4.6", "description": "lowcode designer", "keywords": ["react"], "author": "wwsun <<EMAIL>>", "homepage": "", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/netease/tango.git"}, "scripts": {"clean": "rimraf lib/", "build": "yarn clean && yarn build:esm && yarn build:cjs", "build:esm": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "build:cjs": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "prepublishOnly": "yarn build"}, "peerDependencies": {"react": ">= 16.8", "styled-components": ">= 4"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@music163/request": "^0.2.0", "@music163/tango-context": "^1.1.10", "@music163/tango-core": "^1.4.4", "@music163/tango-helpers": "^1.2.4", "@music163/tango-sandbox": "^1.0.13", "@music163/tango-setting-form": "^1.2.15", "@music163/tango-ui": "^1.4.5", "antd": "^4.24.2", "cash-dom": "^8.1.2", "classnames": "^2.5.1", "color": "^4.2.3", "coral-system": "^1.0.5", "cssjson": "^2.1.3", "date-fns": "^2.29.2", "lodash-es": "^4.17.21", "moment": "^2.30.1", "react-color": "^2.19.3", "react-resizable": "^3.0.5", "semver": "^7.6.2"}, "devDependencies": {"@types/color": "^3.0.5", "@types/react-color": "^3.0.9", "@types/react-resizable": "^3.0.6"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}