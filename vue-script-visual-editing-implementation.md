# Vue SFC Script部分可视化编辑实现

## 功能概述

实现了对Vue SFC的`<script>`部分进行可视化编辑的功能，使用户可以在属性设置器中选择或修改`<script>`中的变量或方法。主要功能包括：

1. **Script解析**：解析Vue SFC的`<script>`和`<script setup>`部分
2. **变量识别**：识别ref、reactive、computed等Vue响应式变量
3. **方法识别**：识别函数声明和箭头函数
4. **可视化绑定**：在属性设置器中选择script中的变量和方法
5. **动态创建**：支持在设计器中创建新的变量

## 技术实现

### 1. Vue Script解析器

#### 1.1 核心解析功能
创建了`vue-script-traverse.ts`，使用Babel解析器分析JavaScript代码：

```typescript
// packages/core/src/helpers/ast/vue-script-traverse.ts
export function parseVueScript(scriptContent: string, isSetupScript = false): VueScriptAnalysis {
  const ast = parse(scriptContent, {
    sourceType: 'module',
    plugins: ['typescript', 'jsx'],
  });
  
  // 遍历AST，提取变量、方法、导入等信息
  traverse(ast, {
    VariableDeclaration(path) { /* 处理变量声明 */ },
    FunctionDeclaration(path) { /* 处理函数声明 */ },
    ImportDeclaration(path) { /* 处理导入语句 */ },
  });
}
```

#### 1.2 支持的变量类型
- **ref变量**：`const count = ref(0)`
- **reactive对象**：`const state = reactive({})`
- **computed属性**：`const fullName = computed(() => ...)`
- **普通变量**：`const message = 'hello'`
- **函数变量**：`const handler = () => {}`

#### 1.3 支持的方法类型
- **函数声明**：`function handleClick() {}`
- **箭头函数**：`const handleSubmit = () => {}`
- **异步函数**：`async function fetchData() {}`

### 2. Vue文件模型扩展

#### 2.1 添加Script分析属性
为`VueViewFile`类添加了script分析相关的属性和方法：

```typescript
// packages/core/src/models/vue-view-file.ts
export class VueViewFile extends AbstractViewFile {
  vueScriptAnalysis: VueScriptAnalysis;
  
  // 获取可用于数据绑定的变量列表
  getDataBindingVariables(): Array<{ label: string; value: string; type: string }> {
    return this.vueScriptAnalysis.variables
      .filter(variable => variable.type !== 'function' && !variable.name.startsWith('_'))
      .map(variable => ({
        label: `${variable.name} (${variable.type})`,
        value: variable.name,
        type: variable.type,
      }));
  }
  
  // 获取可用于事件绑定的方法列表
  getEventBindingMethods(): Array<{ label: string; value: string; params: string[] }> {
    return this.vueScriptAnalysis.methods.map(method => ({
      label: `${method.name}(${method.params.join(', ')})`,
      value: method.name,
      params: method.params,
    }));
  }
}
```

#### 2.2 动态变量管理
支持在设计器中动态添加和更新变量：

```typescript
// 添加新变量
addScriptVariable(variableName: string, variableType: 'ref' | 'reactive' | 'const' | 'let', initialValue?: any): this

// 更新变量值
updateScriptVariable(variableName: string, newValue: any): this
```

### 3. Vue数据绑定设置器

#### 3.1 VueDataBindingSetter
创建了专门的数据绑定设置器，支持：

```typescript
// packages/designer/src/setters/vue-data-binding-setter.tsx
export function VueDataBindingSetter(props: VueDataBindingSetterProps) {
  const bindingTypeOptions = [
    { label: '绑定变量', value: 'variable' },
    { label: 'JS表达式', value: 'expression' },
    { label: '静态值', value: 'static' },
  ];
  
  // 获取可用的数据绑定变量
  const dataBindingVariables = useMemo(() => {
    if (activeFile && typeof activeFile.getDataBindingVariables === 'function') {
      return activeFile.getDataBindingVariables();
    }
    return [];
  }, [activeFile]);
}
```

#### 3.2 绑定类型支持
- **变量绑定**：选择script中定义的响应式变量
- **表达式绑定**：输入JS表达式进行动态计算
- **静态值绑定**：设置固定的静态值

#### 3.3 变量创建功能
支持在设计器中创建新变量：
- 变量名验证（符合JavaScript标识符规范）
- 变量类型选择（ref、reactive、const、let）
- 初始值设置

### 4. Vue方法绑定设置器

#### 4.1 VueMethodBindingSetter
专门用于事件绑定的方法选择器：

```typescript
export function VueMethodBindingSetter(props: VueDataBindingSetterProps) {
  const bindingTypeOptions = [
    { label: '选择方法', value: 'method' },
    { label: '自定义表达式', value: 'expression' },
  ];
  
  // 获取可用的事件绑定方法
  const eventBindingMethods = useMemo(() => {
    if (activeFile && typeof activeFile.getEventBindingMethods === 'function') {
      return activeFile.getEventBindingMethods();
    }
    return [];
  }, [activeFile]);
}
```

#### 4.2 方法绑定类型
- **方法选择**：从script中选择已定义的方法
- **自定义表达式**：输入自定义的方法调用表达式

### 5. 组件原型更新

#### 5.1 nb-table组件
更新了表格组件的data属性绑定：

```typescript
{
  name: ':data',
  title: '表数据',
  setter: 'vueDataBindingSetter',
  group: 'basic',
}
```

#### 5.2 nb-form组件
更新了表单组件的model属性绑定：

```typescript
{
  name: ':model',
  title: '绑定的表单数据对象',
  setter: 'vueDataBindingSetter',
}
```

#### 5.3 nb-input组件
添加了v-model双向数据绑定：

```typescript
{
  name: 'v-model',
  title: '绑定值',
  setter: 'vueDataBindingSetter',
  group: 'basic',
}
```

### 6. 示例实现

#### 6.1 Vue项目示例代码
更新了Vue项目的HomeView示例，包含完整的变量和方法定义：

```vue
<script setup>
import { reactive, ref } from 'vue'

// 表格数据
const tableData = ref([
  { date: '2016-05-03', name: 'Tom', address: 'Los Angeles' },
  { date: '2016-05-02', name: 'John', address: 'New York' },
  { date: '2016-05-04', name: 'Jane', address: 'Chicago' },
])

// 弹窗相关状态
const dialogVisible = ref(false)
const inputValue = ref('')

// 表单数据
const formData = reactive({
  name: '',
  email: '',
  message: '',
})

// 方法定义
const handleConfirm = () => {
  console.log('确认操作，输入值：', inputValue.value)
  dialogVisible.value = false
}

const openDialog = () => {
  dialogVisible.value = true
}
</script>

<template>
  <main>
    <nb-table :data="tableData">
      <!-- 表格列定义 -->
    </nb-table>
    
    <nb-button @click="openDialog">打开弹窗</nb-button>
    
    <nb-dialog v-model="dialogVisible">
      <nb-input v-model="inputValue" />
    </nb-dialog>
  </main>
</template>
```

## 用户体验

### 1. 数据绑定流程
1. 选择需要绑定数据的组件（如nb-table）
2. 在属性面板中找到数据属性（如:data）
3. 选择绑定类型：
   - **绑定变量**：从下拉列表中选择script中定义的变量
   - **JS表达式**：输入动态计算表达式
   - **静态值**：直接输入固定值
4. 如果需要新变量，点击"+"按钮创建

### 2. 变量创建流程
1. 点击数据绑定设置器中的"+"按钮
2. 在弹窗中输入变量信息：
   - 变量名（自动验证格式）
   - 变量类型（ref、reactive、const、let）
   - 初始值（可选）
3. 确认后自动添加到script中并可立即使用

### 3. 方法绑定流程
1. 选择需要绑定事件的组件（如nb-button）
2. 在属性面板的事件分组中找到相应事件（如@click）
3. 选择绑定类型：
   - **选择方法**：从下拉列表中选择script中定义的方法
   - **自定义表达式**：输入自定义的方法调用

## 技术特点

### 1. 完整的Vue语法支持
- 支持Vue 3 Composition API语法
- 支持`<script setup>`和传统`<script>`
- 支持TypeScript语法解析

### 2. 智能变量识别
- 自动识别Vue响应式变量类型
- 过滤私有变量和函数类型变量
- 提供友好的变量类型标识

### 3. 实时同步
- script内容修改后自动重新解析
- 变量列表实时更新
- 支持热重载

### 4. 类型安全
- 完整的TypeScript类型定义
- 变量名格式验证
- 参数类型检查

## 总结

通过以上实现，Vue项目现在完全支持script部分的可视化编辑，用户可以：

- ✅ 在属性设置器中选择script中定义的变量
- ✅ 在属性设置器中选择script中定义的方法
- ✅ 在设计器中动态创建新的响应式变量
- ✅ 支持多种数据绑定类型（变量、表达式、静态值）
- ✅ 完整的Vue 3 Composition API支持
- ✅ 实时的script内容解析和同步

这使得Vue项目的可视化编辑体验更加完整和强大，用户无需手动编写script代码即可实现复杂的数据绑定和交互逻辑。
