{"name": "@music163/tango-core", "version": "1.4.4", "description": "tango core", "author": "wwsun <<EMAIL>>", "homepage": "", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/netease/tango.git"}, "scripts": {"clean": "rimraf lib/", "build": "yarn clean && yarn build:esm && yarn build:cjs", "build:esm": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "build:cjs": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "prepublishOnly": "yarn build"}, "dependencies": {"@babel/generator": "^7.25.6", "@babel/parser": "^7.25.6", "@babel/traverse": "^7.25.6", "@babel/types": "^7.25.6", "@music163/tango-helpers": "^1.2.4", "@types/babel__generator": "^7.6.7", "@types/babel__traverse": "^7.20.6", "@vue/compiler-sfc": "3.5.13", "@vue/compiler-dom": "3.5.13", "lodash-es": "^4.17.21", "mobx": "6.13.2", "node-html-parser": "6.1.13", "path-browserify": "^1.0.1"}, "peerDependencies": {"mobx": "6.9.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}