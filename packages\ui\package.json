{"name": "@music163/tango-ui", "version": "1.4.5", "description": "ui widgets of tango", "keywords": ["react", "ui", "widgets"], "author": "wwsun <<EMAIL>>", "homepage": "", "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/netease/tango.git"}, "scripts": {"clean": "rimraf lib/", "build": "yarn clean && yarn build:esm && yarn build:cjs", "build:esm": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "build:cjs": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "prepublishOnly": "yarn build"}, "peerDependencies": {"react": ">= 16.8", "styled-components": ">= 4"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@codemirror/autocomplete": "^6.16.0", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lint": "^6.7.1", "@codemirror/search": "^6.5.6", "@music163/tango-helpers": "^1.2.4", "@uiw/react-codemirror": "^4.22.0", "antd": "^4.24.2", "classnames": "^2.5.1", "coral-system": "^1.0.5", "eslint-linter-browserify": "^8.51.0", "react-draggable": "^4.4.5", "react-json-view": "^1.21.3", "react-monaco-editor-lite": "^1.3.16"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}