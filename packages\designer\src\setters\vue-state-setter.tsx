import React, { useCallback, useMemo, useState } from 'react';
import { css, Box, Text } from 'coral-system';
import { Input, Select, Switch } from 'antd';
import { FormItemComponentProps } from '@music163/tango-setting-form';
import { useWorkspace, useWorkspaceData } from '@music163/tango-context';
import { wrapCode } from '@music163/tango-helpers';
import { value2code } from '@music163/tango-core';

const wrapperStyle = css`
  .ant-select,
  .ant-input {
    width: 100%;
    margin-bottom: 8px;
  }
`;

export type VueStateSetterProps = FormItemComponentProps<string>;

/**
 * Vue状态绑定设置器
 * 支持v-model、:value、:disabled等Vue特有的数据绑定
 */
export function VueStateSetter(props: VueStateSetterProps) {
  const { value, onChange } = props;
  const [bindingType, setBindingType] = useState<'static' | 'reactive' | 'computed'>('static');
  const [inputValue, setInputValue] = useState('');
  const { expressionVariables } = useWorkspaceData();

  const code = value2code(value);

  const handleChange = useCallback<FormItemComponentProps['onChange']>(
    (nextValue: any, ...args) => {
      if (!nextValue) {
        onChange(undefined);
      }
      if (nextValue !== code) {
        onChange(wrapCode(nextValue), ...args);
      }
    },
    [onChange, code],
  );

  const bindingOptions = [
    { label: '静态值', value: 'static' },
    { label: '响应式数据', value: 'reactive' },
    { label: '计算属性', value: 'computed' },
  ];

  const variableOptions = useMemo(() => {
    const options: Array<{ label: string; value: string }> = [];
    
    expressionVariables.forEach((group) => {
      if (group.children) {
        group.children.forEach((variable) => {
          options.push({
            label: `${group.title}.${variable.title}`,
            value: variable.key,
          });
        });
      }
    });

    return options;
  }, [expressionVariables]);

  const handleBindingTypeChange = (type: 'static' | 'reactive' | 'computed') => {
    setBindingType(type);
    if (type === 'static') {
      handleChange(inputValue);
    }
  };

  const handleInputChange = (val: string) => {
    setInputValue(val);
    if (bindingType === 'static') {
      handleChange(val);
    } else if (bindingType === 'reactive') {
      handleChange(`{{ ${val} }}`);
    } else if (bindingType === 'computed') {
      handleChange(`{{ computed(() => ${val}) }}`);
    }
  };

  return (
    <Box css={wrapperStyle}>
      <Select
        placeholder="选择绑定类型"
        value={bindingType}
        onChange={handleBindingTypeChange}
        options={bindingOptions}
      />
      
      {bindingType === 'static' && (
        <Input
          placeholder="输入静态值"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
        />
      )}
      
      {bindingType === 'reactive' && (
        <Select
          placeholder="选择响应式变量"
          value={inputValue}
          onChange={handleInputChange}
          options={variableOptions}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
        />
      )}
      
      {bindingType === 'computed' && (
        <Input.TextArea
          placeholder="输入计算表达式，如：user.name + ' - ' + user.role"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          rows={3}
        />
      )}
      
      <Box mt="s">
        <Text fontSize="12px" color="text.placeholder">
          {bindingType === 'static' && '静态值将直接作为属性值'}
          {bindingType === 'reactive' && '响应式数据会自动更新视图'}
          {bindingType === 'computed' && '计算属性基于其他数据动态计算'}
        </Text>
      </Box>
    </Box>
  );
}

/**
 * Vue布尔值设置器
 * 支持true/false以及响应式布尔值绑定
 */
export function VueBooleanSetter(props: VueStateSetterProps) {
  const { value, onChange } = props;
  const [bindingType, setBindingType] = useState<'static' | 'reactive'>('static');
  const [boolValue, setBoolValue] = useState(false);
  const [variableValue, setVariableValue] = useState('');
  const { expressionVariables } = useWorkspaceData();

  const code = value2code(value);

  const handleChange = useCallback<FormItemComponentProps['onChange']>(
    (nextValue: any, ...args) => {
      if (nextValue === undefined || nextValue === null) {
        onChange(undefined);
      } else if (nextValue !== code) {
        onChange(wrapCode(nextValue), ...args);
      }
    },
    [onChange, code],
  );

  const variableOptions = useMemo(() => {
    const options: Array<{ label: string; value: string }> = [];
    
    expressionVariables.forEach((group) => {
      if (group.children) {
        group.children.forEach((variable) => {
          options.push({
            label: `${group.title}.${variable.title}`,
            value: variable.key,
          });
        });
      }
    });

    return options;
  }, [expressionVariables]);

  const handleBindingTypeChange = (type: 'static' | 'reactive') => {
    setBindingType(type);
    if (type === 'static') {
      handleChange(boolValue.toString());
    } else {
      handleChange(`{{ ${variableValue} }}`);
    }
  };

  const handleBoolChange = (checked: boolean) => {
    setBoolValue(checked);
    if (bindingType === 'static') {
      handleChange(checked.toString());
    }
  };

  const handleVariableChange = (val: string) => {
    setVariableValue(val);
    if (bindingType === 'reactive') {
      handleChange(`{{ ${val} }}`);
    }
  };

  return (
    <Box css={wrapperStyle}>
      <Select
        placeholder="选择绑定类型"
        value={bindingType}
        onChange={handleBindingTypeChange}
        options={[
          { label: '静态值', value: 'static' },
          { label: '响应式数据', value: 'reactive' },
        ]}
      />
      
      {bindingType === 'static' && (
        <Box display="flex" alignItems="center" mt="s">
          <Switch
            checked={boolValue}
            onChange={handleBoolChange}
          />
          <Text ml="s" fontSize="12px">
            {boolValue ? 'true' : 'false'}
          </Text>
        </Box>
      )}
      
      {bindingType === 'reactive' && (
        <Select
          placeholder="选择布尔变量"
          value={variableValue}
          onChange={handleVariableChange}
          options={variableOptions}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
        />
      )}
    </Box>
  );
}
