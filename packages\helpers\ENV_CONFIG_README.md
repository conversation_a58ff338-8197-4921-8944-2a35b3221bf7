# 环境配置工具 (env-config)

## 概述

环境配置工具是 `@music163/tango-helpers` 包中的一个通用模块，提供了根据不同环境动态设置沙箱基础URL的功能，支持开发、测试、生产和本地环境的自动识别和配置。

## 支持的环境

| 环境 | 环境标识 | 域名 | 说明 |
|------|----------|------|------|
| 开发环境 | `dev` | `//code-hive-dev.wanyol.com` | 开发环境 |
| 测试环境 | `test` | `//code-hive.wanyol.com` | 测试环境 |
| 生产环境 | `production` | `//code-hive.oppoer.me` | 生产环境 |
| 本地开发 | `local` | `//code-hive-dev.wanyol.com` | 本地开发环境，沿用开发环境域名 |

## 环境检测优先级

环境检测按以下优先级进行：

1. **环境变量**：`NODE_ENV` 或 `UMI_ENV`，`NODE_ENV` 为 `development` 时，自动识别为 `local`，其它情况以 `UMI_ENV` 为准
2. **域名判断**：根据当前页面域名自动判断
3. **默认值**：如果以上都无法确定，默认为 `local`

## API 文档

### 基础函数

#### `getCurrentEnvironment(): Environment`

获取当前环境。

```typescript
import { getCurrentEnvironment } from '@music163/tango-helpers';

const currentEnv = getCurrentEnvironment();
console.log('当前环境:', currentEnv); // 'dev' | 'test' | 'production' | 'local'
```

#### `getSandboxBaseUrl(env?: Environment): string`

获取沙箱基础URL。

```typescript
import { getSandboxBaseUrl } from '@music163/tango-helpers';

// 自动检测环境
const baseUrl = getSandboxBaseUrl();

// 手动指定环境
const devUrl = getSandboxBaseUrl('dev');
console.log(devUrl); // '//code-hive-dev.wanyol.com'
```

#### `buildSandboxLink(containerId: string, env?: Environment): string`

构建完整的沙箱链接。

```typescript
import { buildSandboxLink } from '@music163/tango-helpers';

// 自动检测环境
const sandboxUrl = buildSandboxLink('container-123');

// 手动指定环境
const prodUrl = buildSandboxLink('container-123', 'production');
console.log(prodUrl); // '//code-hive.oppoer.me/api/container/container-123/'
```

### 工具函数

#### `getEnvironmentDomainMap(): Record<Environment, string>`

获取环境域名映射配置。

```typescript
import { getEnvironmentDomainMap } from '@music163/tango-helpers';

const domainMap = getEnvironmentDomainMap();
console.log(domainMap);
// {
//   dev: '//code-hive-dev.wanyol.com',
//   test: '//code-hive.wanyol.com',
//   production: '//code-hive.oppoer.me',
//   local: '//code-hive-dev.wanyol.com'
// }
```

#### `isEnvironment(targetEnv: Environment, currentEnv?: Environment): boolean`

检查是否为指定环境。

```typescript
import { isEnvironment } from '@music163/tango-helpers';

// 检查当前环境是否为生产环境
const isProd = isEnvironment('production');

// 检查指定环境
const isDevEnv = isEnvironment('dev', 'dev'); // true
```

### 便捷检查函数

#### `isProduction(): boolean`

检查是否为生产环境。

```typescript
import { isProduction } from '@music163/tango-helpers';

if (isProduction()) {
  console.log('当前为生产环境');
}
```

#### `isDevelopment(): boolean`

检查是否为开发环境。

#### `isTest(): boolean`

检查是否为测试环境。

#### `isLocal(): boolean`

检查是否为本地环境。

## 使用示例

### 在 React 组件中使用

```typescript
import React, { useEffect, useState } from 'react';
import { 
  getCurrentEnvironment, 
  buildSandboxLink, 
  isProduction 
} from '@music163/tango-helpers';

const MyComponent: React.FC = () => {
  const [sandboxUrl, setSandboxUrl] = useState('');
  
  useEffect(() => {
    const containerId = new URLSearchParams(window.location.search).get('id');
    if (containerId) {
      const url = buildSandboxLink(containerId);
      setSandboxUrl(url);
    }
  }, []);

  return (
    <div>
      <p>当前环境: {getCurrentEnvironment()}</p>
      <p>是否生产环境: {isProduction() ? '是' : '否'}</p>
      <p>沙箱URL: {sandboxUrl}</p>
    </div>
  );
};
```

### 在配置文件中使用

```typescript
import { getCurrentEnvironment, getSandboxBaseUrl } from '@music163/tango-helpers';

const config = {
  apiBaseUrl: getSandboxBaseUrl(),
  debug: !isProduction(),
  environment: getCurrentEnvironment(),
};

export default config;
```

## 类型定义

```typescript
export type Environment = 'dev' | 'test' | 'production' | 'local';
```
